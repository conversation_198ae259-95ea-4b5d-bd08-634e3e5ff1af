// إعدادات التطبيق الأساسية
class BagFactorySystem {
    constructor() {
        this.currentUser = null;
        this.users = this.loadUsers();
        this.products = this.loadProducts();
        this.workers = this.loadWorkers();
        this.attendance = this.loadAttendance();
        this.inventory = this.loadInventory();
        this.payroll = this.loadPayroll();
        this.materials = this.loadMaterials(); // New: Materials data
        this.categories = this.loadCategories(); // New: Categories data
        this.workerWithdrawals = this.loadWorkerWithdrawals(); // New: Worker withdrawals data
        this.inventoryMovements = this.loadInventoryMovements(); // New: Detailed inventory movements tracking

        this.initializeApp();
    }

    // تحميل البيانات من localStorage
    loadUsers() {
        const defaultUsers = {
            'admin': {
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                name: 'المدير العام'
            }
        };
        return JSON.parse(localStorage.getItem('bagFactory_users')) || defaultUsers;
    }

    loadProducts() {
        let savedProducts = JSON.parse(localStorage.getItem('bagFactory_products'));
        if (savedProducts && savedProducts.length > 0) {
            // Ensure totalPrice is calculated for existing products if not present
            savedProducts = savedProducts.map(product => {
                if (product.totalPrice === undefined || product.totalPrice === null) {
                    product.totalPrice = (parseFloat(product.price) || 0) * (parseInt(product.quantity) || 0);
                }
                return product;
            });
            return savedProducts;
        }

        // بيانات تجريبية للمنتجات
        return [
            {
                id: 'prod1',
                code: 'SH001',
                name: 'شنطة يد جلد طبيعي',
                material: 'جلد طبيعي',
                category: 'شنط يد',
                style: 'كلاسيك',
                price: 250,
                quantity: 15,
                barcode: 'BF12345678',
                description: 'شنطة يد أنيقة من الجلد الطبيعي',
                totalPrice: 250 * 15, // Calculate total price for initial data
                createdAt: new Date().toISOString()
            },
            {
                id: 'prod2',
                code: 'SH002',
                name: 'شنطة ظهر مدرسية',
                material: 'قماش',
                category: 'شنط مدرسية',
                style: 'رياضي',
                price: 120,
                quantity: 8,
                barcode: 'BF12345679',
                description: 'شنطة ظهر مناسبة للطلاب',
                totalPrice: 120 * 8, // Calculate total price for initial data
                createdAt: new Date().toISOString()
            },
            {
                id: 'prod3',
                code: 'SH003',
                name: 'شنطة سفر كبيرة',
                material: 'جلد صناعي',
                category: 'شنط سفر',
                style: 'عملي',
                price: 400,
                quantity: 5,
                barcode: 'BF12345680',
                description: 'شنطة سفر واسعة ومتينة',
                totalPrice: 400 * 5, // Calculate total price for initial data
                createdAt: new Date().toISOString()
            }
        ];
    }

    loadWorkers() {
        const savedWorkers = JSON.parse(localStorage.getItem('bagFactory_workers'));
        if (savedWorkers && savedWorkers.length > 0) {
            return savedWorkers;
        }

        // بيانات تجريبية للعمال
        return [
            {
                id: 'worker1',
                name: 'أحمد محمد علي',
                job: 'خياط',
                dailySalary: 150,
                phone: '01012345678',
                address: 'القاهرة',
                hireDate: '2024-01-15',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'worker2',
                name: 'فاطمة حسن',
                job: 'قصاص',
                dailySalary: 120,
                phone: '01098765432',
                address: 'الجيزة',
                hireDate: '2024-02-01',
                status: 'active',
                createdAt: new Date().toISOString()
            },
            {
                id: 'worker3',
                name: 'محمود السيد',
                job: 'مشرف',
                dailySalary: 200,
                phone: '01055555555',
                address: 'القاهرة',
                hireDate: '2023-12-01',
                status: 'active',
                createdAt: new Date().toISOString()
            }
        ];
    }

    loadAttendance() {
        return JSON.parse(localStorage.getItem('bagFactory_attendance')) || {};
    }

    loadInventory() {
        return JSON.parse(localStorage.getItem('bagFactory_inventory')) || {};
    }

    loadPayroll() {
        return JSON.parse(localStorage.getItem('bagFactory_payroll')) || {};
    }

    loadMaterials() {
        const savedMaterials = JSON.parse(localStorage.getItem('bagFactory_materials'));
        if (savedMaterials && savedMaterials.length > 0) {
            return savedMaterials;
        }
        return [
            { id: 'mat1', name: 'جلد طبيعي', description: 'جلد بقر عالي الجودة', productCount: 0 },
            { id: 'mat2', name: 'جلد صناعي', description: 'جلد صناعي مقاوم للماء', productCount: 0 },
            { id: 'mat3', name: 'قماش', description: 'قماش كتان متين', productCount: 0 }
        ];
    }

    loadCategories() {
        const savedCategories = JSON.parse(localStorage.getItem('bagFactory_categories'));
        if (savedCategories && savedCategories.length > 0) {
            return savedCategories;
        }
        return [
            { id: 'cat1', name: 'شنط يد', description: 'شنط يد نسائية ورجالية', productCount: 0 },
            { id: 'cat2', name: 'شنط ظهر', description: 'شنط ظهر مدرسية ورياضية', productCount: 0 },
            { id: 'cat3', name: 'محافظ', description: 'محافظ جلدية وقماشية', productCount: 0 }
        ];
    }

    // حفظ البيانات في localStorage
    saveUsers() {
        localStorage.setItem('bagFactory_users', JSON.stringify(this.users));
    }

    saveProducts() {
        localStorage.setItem('bagFactory_products', JSON.stringify(this.products));
    }

    saveWorkers() {
        localStorage.setItem('bagFactory_workers', JSON.stringify(this.workers));
    }

    saveAttendance() {
        localStorage.setItem('bagFactory_attendance', JSON.stringify(this.attendance));
    }

    saveInventory() {
        localStorage.setItem('bagFactory_inventory', JSON.stringify(this.inventory));
    }

    savePayroll() {
        localStorage.setItem('bagFactory_payroll', JSON.stringify(this.payroll));
    }

    saveMaterials() {
        localStorage.setItem('bagFactory_materials', JSON.stringify(this.materials));
    }

    saveCategories() {
        localStorage.setItem('bagFactory_categories', JSON.stringify(this.categories));
    }

    // تهيئة التطبيق
    initializeApp() {
        this.setupEventListeners();
        this.checkLoginStatus();
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // البحث في المنتجات
        document.getElementById('productSearch').addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });

        // فلترة المنتجات
        document.getElementById('categoryFilter').addEventListener('change', (e) => {
            this.filterProducts();
        });

        // ترتيب المنتجات
        document.getElementById('sortBy').addEventListener('change', (e) => {
            this.sortProducts(e.target.value);
        });

        // تحديث إعدادات المستخدم
        const userSettingsForm = document.getElementById('userSettingsForm');
        if (userSettingsForm) {
            userSettingsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateUserSettings();
            });
        }

        // البحث في سجلات سحب العمال
        const withdrawalSearch = document.getElementById('withdrawalSearch');
        if (withdrawalSearch) {
            withdrawalSearch.addEventListener('input', (e) => {
                this.applyWithdrawalFilters();
            });
        }

        // البحث بالتاريخ في سجلات سحب العمال
        const withdrawalDateFilter = document.getElementById('withdrawalDateFilter');
        if (withdrawalDateFilter) {
            withdrawalDateFilter.addEventListener('change', (e) => {
                this.applyWithdrawalFilters();
            });
        }

        // فلترة سجلات سحب العمال حسب العامل
        const withdrawalWorkerFilter = document.getElementById('withdrawalWorkerFilter');
        if (withdrawalWorkerFilter) {
            withdrawalWorkerFilter.addEventListener('change', (e) => {
                this.applyWithdrawalFilters();
            });
        }

        // البحث في سجلات حركة المخزون
        const inventoryMovementSearch = document.getElementById('inventoryMovementSearch');
        if (inventoryMovementSearch) {
            inventoryMovementSearch.addEventListener('input', (e) => {
                this.applyInventoryMovementFilters();
            });
        }

        // فلترة حركة المخزون بالتاريخ من
        const inventoryMovementDateFrom = document.getElementById('inventoryMovementDateFrom');
        if (inventoryMovementDateFrom) {
            inventoryMovementDateFrom.addEventListener('change', (e) => {
                this.applyInventoryMovementFilters();
            });
        }

        // فلترة حركة المخزون بالتاريخ إلى
        const inventoryMovementDateTo = document.getElementById('inventoryMovementDateTo');
        if (inventoryMovementDateTo) {
            inventoryMovementDateTo.addEventListener('change', (e) => {
                this.applyInventoryMovementFilters();
            });
        }

        // فلترة حركة المخزون بنوع العملية
        const inventoryMovementTypeFilter = document.getElementById('inventoryMovementTypeFilter');
        if (inventoryMovementTypeFilter) {
            inventoryMovementTypeFilter.addEventListener('change', (e) => {
                this.applyInventoryMovementFilters();
            });
        }
    }

    // فحص حالة تسجيل الدخول
    checkLoginStatus() {
        // إزالة المستخدم المحفوظ لإجبار تسجيل الدخول في كل مرة
        localStorage.removeItem('bagFactory_currentUser');
        this.currentUser = null;

        // عرض شاشة تسجيل الدخول دائماً
        document.getElementById('loginScreen').classList.remove('d-none');
        document.getElementById('mainApp').classList.add('d-none');
    }

    // معالجة تسجيل الدخول
    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // التحقق من صحة البيانات
        if (!username || !password) {
            alert('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // التحقق من وجود المستخدم وصحة كلمة المرور
        if (this.users[username] && this.users[username].password === password) {
            // التحقق من صلاحيات المدير
            if (this.users[username].role !== 'admin') {
                alert('يجب أن تكون مديراً للوصول إلى النظام');
                return;
            }

            this.currentUser = this.users[username];
            // عدم حفظ المستخدم في localStorage لإجبار تسجيل الدخول في كل مرة
            this.showMainApp();
        } else {
            alert('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
    }

    // عرض التطبيق الرئيسي
    showMainApp() {
        document.getElementById('loginScreen').classList.add('d-none');
        document.getElementById('mainApp').classList.remove('d-none');
        document.getElementById('currentUser').textContent = this.currentUser.name;
        
        this.updateDashboard();
        this.loadProductsTable();
        this.loadMaterialsTable(); // Load materials table on app start
        this.loadCategoriesTable(); // Load categories table on app start
        this.populateProductMaterialAndCategoryDropdowns(); // Populate dropdowns on app start
    }

    // تسجيل الخروج
    logout() {
        // مسح بيانات المستخدم الحالي
        this.currentUser = null;

        // عرض شاشة تسجيل الدخول
        document.getElementById('loginScreen').classList.remove('d-none');
        document.getElementById('mainApp').classList.add('d-none');

        // إعادة تعيين نموذج تسجيل الدخول
        document.getElementById('loginForm').reset();

        // التركيز على حقل اسم المستخدم
        setTimeout(() => {
            document.getElementById('username').focus();
        }, 100);

        // التركيز على حقل اسم المستخدم
        document.getElementById('username').focus();
    }

    // عرض قسم معين
    showSection(sectionName) {
        // إخفاء جميع الأقسام
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });

        // إزالة الفئة النشطة من جميع عناصر القائمة
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });

        // عرض القسم المحدد
        document.getElementById(sectionName).classList.add('active');

        // إضافة الفئة النشطة لعنصر القائمة
        event.target.classList.add('active');

        // تحديث المحتوى حسب القسم
        switch(sectionName) {
            case 'dashboard':
                this.updateDashboard();
                break;
            case 'products':
                this.loadProductsTable();
                break;
            case 'inventory':
                this.updateInventoryStats();
                this.loadInventoryTable();
                this.loadWorkerWithdrawalsTable();
                this.loadInventoryMovementTable(); // تحميل جدول حركة المخزون الجديد
                break;
            case 'workers':
                this.loadWorkersTable();
                break;
            case 'attendance':
                this.loadAttendanceTable();
                break;
            case 'payroll':
                this.loadPayrollTable();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'settings':
                this.loadUsersTable();
                break;
            case 'materials': // New case for materials and categories section
                this.loadMaterialsTable();
                this.loadCategoriesTable();
                break;
        }
    }

    // تحديث لوحة التحكم
    updateDashboard() {
        document.getElementById('totalProducts').textContent = this.products.length;
        
        const totalStock = this.products.reduce((sum, product) => sum + (product.quantity || 0), 0);
        document.getElementById('totalStock').textContent = totalStock;
        
        document.getElementById('totalWorkers').textContent = this.workers.length;
        
        // حساب إجمالي المرتبات الشهرية
        const monthlyPayroll = this.calculateMonthlyPayroll();
        document.getElementById('monthlyPayroll').textContent = monthlyPayroll.toLocaleString() + ' ج.م';
        
        this.updateCharts();
    }

    // حساب المرتبات الشهرية
    calculateMonthlyPayroll() {
        const currentMonth = new Date().getMonth();
        const currentYear = new Date().getFullYear();
        
        return this.workers.reduce((total, worker) => {
            const monthlyDays = this.getWorkerAttendanceDays(worker.id, currentMonth, currentYear);
            return total + (worker.dailySalary * monthlyDays);
        }, 0);
    }

    // الحصول على أيام حضور العامل
    getWorkerAttendanceDays(workerId, month, year) {
        const attendanceKey = `${year}-${month}`;
        if (this.attendance[attendanceKey] && this.attendance[attendanceKey][workerId]) {
            return this.attendance[attendanceKey][workerId].presentDays || 0;
        }
        return 0;
    }

    // تحديث الرسوم البيانية
    updateCharts() {
        this.updateProductionChart();
        this.updateAttendanceChart();
    }

    // رسم بياني للإنتاج
    updateProductionChart() {
        const ctx = document.getElementById('productionChart').getContext('2d');
        
        // بيانات وهمية للإنتاج الأسبوعي
        const productionData = {
            labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
            datasets: [{
                label: 'عدد الشنط المنتجة',
                data: [120, 150, 180, 140, 200, 160, 100],
                backgroundColor: 'rgba(52, 152, 219, 0.2)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 2,
                fill: true
            }]
        };

        new Chart(ctx, {
            type: 'line',
            data: productionData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        labels: {
                            font: {
                                family: 'Arial'
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // رسم بياني للحضور
    updateAttendanceChart() {
        const ctx = document.getElementById('attendanceChart').getContext('2d');
        
        const attendanceData = {
            labels: ['حاضر', 'غائب', 'متأخر'],
            datasets: [{
                data: [85, 10, 5],
                backgroundColor: [
                    'rgba(39, 174, 96, 0.8)',
                    'rgba(231, 76, 60, 0.8)',
                    'rgba(243, 156, 18, 0.8)'
                ],
                borderWidth: 0
            }]
        };

        new Chart(ctx, {
            type: 'doughnut',
            data: attendanceData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            font: {
                                family: 'Arial'
                            }
                        }
                    }
                }
            }
        });
    }

    // تحميل جدول المنتجات
    loadProductsTable() {
        const tbody = document.getElementById('productsTable');
        tbody.innerHTML = '';

        this.products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.code}</td>
                <td>${product.name}</td>
                <td>${product.material}</td>
                <td>${product.category}</td>
                <td>${product.price} ج.م</td>
                <td>
                    <span class="badge ${product.quantity < 10 ? 'bg-danger' : 'bg-success'}">
                        ${product.quantity}
                    </span>
                </td>
                <td>${product.totalPrice ? product.totalPrice.toLocaleString() + ' ج.م' : (product.price * product.quantity).toLocaleString() + ' ج.م'}</td>
                <td>${product.barcode}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editProduct('${product.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteProduct('${product.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        this.updateCategoryFilter();
        this.populateProductMaterialAndCategoryDropdowns(); // Update dropdowns when products table loads
    }

    // تحديث فلتر التصنيفات
    updateCategoryFilter() {
        const categoryFilter = document.getElementById('categoryFilter');
        const categories = [...new Set(this.products.map(p => p.category))];
        
        categoryFilter.innerHTML = '<option value="">جميع التصنيفات</option>';
        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });
    }

    // ملء قوائم الخامات والتصنيفات في نموذج إضافة/تعديل المنتج
    populateProductMaterialAndCategoryDropdowns() {
        const materialSelect = document.getElementById('productMaterial');
        const categorySelect = document.getElementById('productCategory');

        // Clear existing options except the first (placeholder)
        materialSelect.innerHTML = '<option value="">اختر نوع الخامة</option>';
        categorySelect.innerHTML = '<option value="">اختر التصنيف</option>';

        this.materials.forEach(material => {
            const option = document.createElement('option');
            option.value = material.name;
            option.textContent = material.name;
            materialSelect.appendChild(option);
        });

        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.name;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
    }

    // البحث في المنتجات
    searchProducts(searchTerm) {
        const filteredProducts = this.products.filter(product => 
            product.name.includes(searchTerm) || 
            product.code.includes(searchTerm) || 
            product.barcode.includes(searchTerm)
        );
        this.displayFilteredProducts(filteredProducts);
    }

    // فلترة المنتجات
    filterProducts() {
        const category = document.getElementById('categoryFilter').value;
        const filteredProducts = category ? 
            this.products.filter(product => product.category === category) : 
            this.products;
        this.displayFilteredProducts(filteredProducts);
    }

    // ترتيب المنتجات
    sortProducts(sortBy) {
        const sortedProducts = [...this.products].sort((a, b) => {
            switch(sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'price':
                    return a.price - b.price;
                case 'quantity':
                    return a.quantity - b.quantity;
                default:
                    return 0;
            }
        });
        this.displayFilteredProducts(sortedProducts);
    }

    // عرض المنتجات المفلترة
    displayFilteredProducts(products) {
        const tbody = document.getElementById('productsTable');
        tbody.innerHTML = '';

        products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.code}</td>
                <td>${product.name}</td>
                <td>${product.material}</td>
                <td>${product.category}</td>
                <td>${product.price} ج.م</td>
                <td>
                    <span class="badge ${product.quantity < 10 ? 'bg-danger' : 'bg-success'}">
                        ${product.quantity}
                    </span>
                </td>
                <td>${product.totalPrice ? product.totalPrice.toLocaleString() + ' ج.م' : (product.price * product.quantity).toLocaleString() + ' ج.م'}</td>
                <td>${product.barcode}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editProduct('${product.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteProduct('${product.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // إضافة منتج جديد
    addProduct() {
        try {
            // التحقق من صحة البيانات
            const code = document.getElementById('productCode').value.trim();
            const name = document.getElementById('productName').value.trim();
            const material = document.getElementById('productMaterial').value;
            const category = document.getElementById('productCategory').value;
            const price = parseFloat(document.getElementById('productPrice').value);
            const quantity = parseInt(document.getElementById('productQuantity').value);
            const barcode = document.getElementById('productBarcode').value.trim();

            // التحقق من الحقول المطلوبة
            if (!code || !name || !material || !category || isNaN(price) || isNaN(quantity)) {
                this.showError('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
                return;
            }

            if (price < 0) {
                this.showError('السعر يجب أن يكون أكبر من أو يساوي صفر');
                return;
            }

            if (quantity < 0) {
                this.showError('الكمية يجب أن تكون أكبر من أو تساوي صفر');
                return;
            }

            const product = {
                id: this.generateId(),
                code: code,
                name: name,
                material: material,
                category: category,
                style: document.getElementById('productStyle').value.trim(),
                price: price,
                quantity: quantity,
                barcode: barcode,
                description: document.getElementById('productDescription').value.trim(),
                totalPrice: price * quantity,
                createdAt: new Date().toISOString()
            };

            // التحقق من عدم تكرار الكود أو الباركود
            if (this.products.some(p => p.code === product.code)) {
                this.showError('كود المنتج موجود بالفعل');
                return;
            }

            if (product.barcode && this.products.some(p => p.barcode === product.barcode)) {
                this.showError('الباركود موجود بالفعل');
                return;
            }

            this.products.push(product);
            this.saveProducts();
            this.loadProductsTable();
            this.updateDashboard();
            this.updateMaterialProductCounts();
            this.updateCategoryProductCounts();

            // إغلاق النافذة المنبثقة وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
            modal.hide();
            document.getElementById('addProductForm').reset();

            this.showSuccess('تم إضافة المنتج بنجاح');
        } catch (error) {
            console.error('خطأ في إضافة المنتج:', error);
            this.showError('حدث خطأ أثناء إضافة المنتج. يرجى المحاولة مرة أخرى.');
        }
    }

    // تعديل منتج
    editProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // ملء النموذج ببيانات المنتج
        document.getElementById('productCode').value = product.code;
        document.getElementById('productName').value = product.name;
        document.getElementById('productMaterial').value = product.material;
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productStyle').value = product.style || '';
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productQuantity').value = product.quantity;
        document.getElementById('productBarcode').value = product.barcode || '';
        document.getElementById('productDescription').value = product.description || '';

        // تغيير عنوان النافذة والزر
        document.querySelector('#addProductModal .modal-title').innerHTML =
            '<i class="bi bi-pencil me-2"></i>تعديل المنتج';
        document.querySelector('#addProductModal .modal-footer .btn-primary').innerHTML =
            '<i class="bi bi-check-circle me-2"></i>حفظ التعديلات';

        // تغيير وظيفة الزر
        document.querySelector('#addProductModal .modal-footer .btn-primary').onclick = () => {
            this.updateProduct(productId);
        };

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
        modal.show();
    }

    // تحديث منتج
    updateProduct(productId) {
        const productIndex = this.products.findIndex(p => p.id === productId);
        if (productIndex === -1) return;

        const updatedProduct = {
            ...this.products[productIndex],
            code: document.getElementById('productCode').value,
            name: document.getElementById('productName').value,
            material: document.getElementById('productMaterial').value,
            category: document.getElementById('productCategory').value,
            style: document.getElementById('productStyle').value,
            price: parseFloat(document.getElementById('productPrice').value),
            quantity: parseInt(document.getElementById('productQuantity').value),
            barcode: document.getElementById('productBarcode').value,
            description: document.getElementById('productDescription').value,
            totalPrice: parseFloat(document.getElementById('productPrice').value) * parseInt(document.getElementById('productQuantity').value), // Recalculate total price
            updatedAt: new Date().toISOString()
        };

        this.products[productIndex] = updatedProduct;
        this.saveProducts();
        this.loadProductsTable();
        this.updateDashboard();
        this.updateMaterialProductCounts(); // Update material counts
        this.updateCategoryProductCounts(); // Update category counts

        // إغلاق النافذة وإعادة تعيين النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
        modal.hide();
        this.resetProductModal();

        alert('تم تحديث المنتج بنجاح');
    }

    // حذف منتج
    deleteProduct(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) {
            this.showError('المنتج غير موجود');
            return;
        }

        this.showConfirm(
            `هل أنت متأكد من حذف المنتج "${product.name}"؟ هذا الإجراء لا يمكن التراجع عنه.`,
            () => {
                try {
                    this.products = this.products.filter(p => p.id !== productId);
                    this.saveProducts();
                    this.loadProductsTable();
                    this.updateDashboard();
                    this.updateMaterialProductCounts();
                    this.updateCategoryProductCounts();
                    this.showSuccess('تم حذف المنتج بنجاح');
                } catch (error) {
                    console.error('خطأ في حذف المنتج:', error);
                    this.showError('حدث خطأ أثناء حذف المنتج');
                }
            }
        );
    }

    // إعادة تعيين نافذة المنتج
    resetProductModal() {
        document.querySelector('#addProductModal .modal-title').innerHTML =
            '<i class="bi bi-bag-plus me-2"></i>إضافة منتج جديد';
        document.querySelector('#addProductModal .modal-footer .btn-primary').innerHTML =
            '<i class="bi bi-check-circle me-2"></i>إضافة المنتج';
        document.querySelector('#addProductModal .modal-footer .btn-primary').onclick = () => {
            this.addProduct();
        };
        document.getElementById('addProductForm').reset();
    }

    // توليد باركود
    generateBarcode() {
        const barcode = 'BF' + Date.now().toString().slice(-8);
        document.getElementById('productBarcode').value = barcode;
    }

    // إضافة خامة جديدة
    addMaterial() {
        const material = {
            id: this.generateId(),
            name: document.getElementById('materialName').value,
            description: document.getElementById('materialDescription').value,
            productCount: 0,
            createdAt: new Date().toISOString()
        };

        if (this.materials.some(m => m.name === material.name)) {
            alert('اسم الخامة موجود بالفعل');
            return;
        }

        this.materials.push(material);
        this.saveMaterials();
        this.loadMaterialsTable();
        alert('تم إضافة الخامة بنجاح');
        const modal = bootstrap.Modal.getInstance(document.getElementById('addMaterialModal'));
        modal.hide();
        document.getElementById('addMaterialForm').reset();
    }

    // تعديل خامة
    editMaterial(materialId) {
        const material = this.materials.find(m => m.id === materialId);
        if (!material) return;

        document.getElementById('editMaterialId').value = material.id;
        document.getElementById('editMaterialName').value = material.name;
        document.getElementById('editMaterialDescription').value = material.description;

        const modal = new bootstrap.Modal(document.getElementById('editMaterialModal'));
        modal.show();
    }

    // تحديث خامة
    updateMaterial() {
        const materialId = document.getElementById('editMaterialId').value;
        const materialIndex = this.materials.findIndex(m => m.id === materialId);
        if (materialIndex === -1) return;

        const updatedMaterial = {
            ...this.materials[materialIndex],
            name: document.getElementById('editMaterialName').value,
            description: document.getElementById('editMaterialDescription').value,
            updatedAt: new Date().toISOString()
        };

        if (this.materials.some((m, i) => m.name === updatedMaterial.name && i !== materialIndex)) {
            alert('اسم الخامة موجود بالفعل');
            return;
        }

        this.materials[materialIndex] = updatedMaterial;
        this.saveMaterials();
        this.loadMaterialsTable();
        alert('تم تحديث الخامة بنجاح');
        const modal = bootstrap.Modal.getInstance(document.getElementById('editMaterialModal'));
        modal.hide();
    }

    // حذف خامة
    deleteMaterial(materialId) {
        if (confirm('هل أنت متأكد من حذف هذه الخامة؟')) {
            this.materials = this.materials.filter(m => m.id !== materialId);
            this.saveMaterials();
            this.loadMaterialsTable();
            alert('تم حذف الخامة بنجاح');
        }
    }

    // إضافة تصنيف جديد
    addCategory() {
        const category = {
            id: this.generateId(),
            name: document.getElementById('categoryName').value,
            description: document.getElementById('categoryDescription').value,
            productCount: 0,
            createdAt: new Date().toISOString()
        };

        if (this.categories.some(c => c.name === category.name)) {
            alert('اسم التصنيف موجود بالفعل');
            return;
        }

        this.categories.push(category);
        this.saveCategories();
        this.loadCategoriesTable();
        alert('تم إضافة التصنيف بنجاح');
        const modal = bootstrap.Modal.getInstance(document.getElementById('addCategoryModal'));
        modal.hide();
        document.getElementById('addCategoryForm').reset();
    }

    // تعديل تصنيف
    editCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (!category) return;

        document.getElementById('editCategoryId').value = category.id;
        document.getElementById('editCategoryName').value = category.name;
        document.getElementById('editCategoryDescription').value = category.description;

        const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
        modal.show();
    }

    // تحديث تصنيف
    updateCategory() {
        const categoryId = document.getElementById('editCategoryId').value;
        const categoryIndex = this.categories.findIndex(c => c.id === categoryId);
        if (categoryIndex === -1) return;

        const updatedCategory = {
            ...this.categories[categoryIndex],
            name: document.getElementById('editCategoryName').value,
            description: document.getElementById('editCategoryDescription').value,
            updatedAt: new Date().toISOString()
        };

        if (this.categories.some((c, i) => c.name === updatedCategory.name && i !== categoryIndex)) {
            alert('اسم التصنيف موجود بالفعل');
            return;
        }

        this.categories[categoryIndex] = updatedCategory;
        this.saveCategories();
        this.loadCategoriesTable();
        alert('تم تحديث التصنيف بنجاح');
        const modal = bootstrap.Modal.getInstance(document.getElementById('editCategoryModal'));
        modal.hide();
    }

    // حذف تصنيف
    deleteCategory(categoryId) {
        if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
            this.categories = this.categories.filter(c => c.id !== categoryId);
            this.saveCategories();
            this.loadCategoriesTable();
            alert('تم حذف التصنيف بنجاح');
        }
    }

    // إضافة عامل جديد
    addWorker() {
        try {
            // التحقق من صحة البيانات
            const name = document.getElementById('workerName').value.trim();
            const job = document.getElementById('workerJob').value;
            const dailySalary = parseFloat(document.getElementById('workerSalary').value);
            const phone = document.getElementById('workerPhone').value.trim();
            const address = document.getElementById('workerAddress').value.trim();
            const hireDate = document.getElementById('workerHireDate').value;

            // التحقق من الحقول المطلوبة
            if (!name || !job || isNaN(dailySalary) || !hireDate) {
                this.showError('يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
                return;
            }

            if (dailySalary < 0) {
                this.showError('الراتب اليومي يجب أن يكون أكبر من أو يساوي صفر');
                return;
            }

            // التحقق من عدم تكرار الاسم
            if (this.workers.some(w => w.name.toLowerCase() === name.toLowerCase())) {
                this.showWarning('يوجد عامل بنفس الاسم بالفعل');
            }

            const worker = {
                id: this.generateId(),
                name: name,
                job: job,
                dailySalary: dailySalary,
                phone: phone,
                address: address,
                hireDate: hireDate,
                status: 'active',
                createdAt: new Date().toISOString()
            };

            this.workers.push(worker);
            this.saveWorkers();
            this.loadWorkersTable();
            this.updateDashboard();

            // إغلاق النافذة وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('addWorkerModal'));
            modal.hide();
            document.getElementById('addWorkerForm').reset();

            this.showSuccess('تم إضافة العامل بنجاح');
        } catch (error) {
            console.error('خطأ في إضافة العامل:', error);
            this.showError('حدث خطأ أثناء إضافة العامل. يرجى المحاولة مرة أخرى.');
        }
    }

    // تحميل جدول الخامات
    loadMaterialsTable() {
        const tbody = document.getElementById('materialsTable');
        if (!tbody) return;

        tbody.innerHTML = '';
        this.materials.forEach(material => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${material.name}</td>
                <td>${material.description || ''}</td>
                <td>${material.productCount}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editMaterial('${material.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteMaterial('${material.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
        this.updateMaterialProductCounts();
    }

    // تحميل جدول التصنيفات
    loadCategoriesTable() {
        const tbody = document.getElementById('categoriesTable');
        if (!tbody) return;

        tbody.innerHTML = '';
        this.categories.forEach(category => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${category.name}</td>
                <td>${category.description || ''}</td>
                <td>${category.productCount}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editCategory('${category.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteCategory('${category.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
        this.updateCategoryProductCounts();
    }

    // تحديث عدد المنتجات لكل خامة
    updateMaterialProductCounts() {
        this.materials.forEach(material => {
            material.productCount = this.products.filter(p => p.material === material.name).length;
        });
        this.saveMaterials();
        // No need to re-render here, loadMaterialsTable() will be called by showSection
    }

    // تحديث عدد المنتجات لكل تصنيف
    updateCategoryProductCounts() {
        this.categories.forEach(category => {
            category.productCount = this.products.filter(p => p.category === category.name).length;
        });
        this.saveCategories();
        // No need to re-render here, loadCategoriesTable() will be called by showSection
    }

    // تحميل جدول العمال
    loadWorkersTable() {
        const tbody = document.getElementById('workersTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.workers.forEach((worker, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${index + 1}</td>
                <td>${worker.name}</td>
                <td>${worker.job}</td>
                <td>${worker.dailySalary} ج.م</td>
                <td>${new Date(worker.hireDate).toLocaleDateString('ar-EG')}</td>
                <td>
                    <span class="badge ${worker.status === 'active' ? 'bg-success' : 'bg-danger'}">
                        ${worker.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.editWorker('${worker.id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteWorker('${worker.id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // تعديل عامل
    editWorker(workerId) {
        const worker = this.workers.find(w => w.id === workerId);
        if (!worker) return;

        // ملء النموذج ببيانات العامل
        document.getElementById('workerName').value = worker.name;
        document.getElementById('workerJob').value = worker.job;
        document.getElementById('workerSalary').value = worker.dailySalary;
        document.getElementById('workerPhone').value = worker.phone || '';
        document.getElementById('workerAddress').value = worker.address || '';
        document.getElementById('workerHireDate').value = worker.hireDate;

        // تغيير عنوان النافذة والزر
        document.querySelector('#addWorkerModal .modal-title').innerHTML =
            '<i class="bi bi-pencil me-2"></i>تعديل العامل';
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').innerHTML =
            '<i class="bi bi-check-circle me-2"></i>حفظ التعديلات';

        // تغيير وظيفة الزر
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').onclick = () => {
            this.updateWorker(workerId);
        };

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('addWorkerModal'));
        modal.show();
    }

    // تحديث عامل
    updateWorker(workerId) {
        const workerIndex = this.workers.findIndex(w => w.id === workerId);
        if (workerIndex === -1) return;

        const updatedWorker = {
            ...this.workers[workerIndex],
            name: document.getElementById('workerName').value,
            job: document.getElementById('workerJob').value,
            dailySalary: parseFloat(document.getElementById('workerSalary').value),
            phone: document.getElementById('workerPhone').value,
            address: document.getElementById('workerAddress').value,
            hireDate: document.getElementById('workerHireDate').value,
            updatedAt: new Date().toISOString()
        };

        this.workers[workerIndex] = updatedWorker;
        this.saveWorkers();
        this.loadWorkersTable();
        this.updateDashboard();

        // إغلاق النافذة وإعادة تعيين النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('addWorkerModal'));
        modal.hide();
        this.resetWorkerModal();

        alert('تم تحديث العامل بنجاح');
    }

    // حذف عامل
    deleteWorker(workerId) {
        if (confirm('هل أنت متأكد من حذف هذا العامل؟')) {
            this.workers = this.workers.filter(w => w.id !== workerId);
            this.saveWorkers();
            this.loadWorkersTable();
            this.updateDashboard();
            alert('تم حذف العامل بنجاح');
        }
    }

    // إعادة تعيين نافذة العامل
    resetWorkerModal() {
        document.querySelector('#addWorkerModal .modal-title').innerHTML =
            '<i class="bi bi-person-plus me-2"></i>إضافة عامل جديد';
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').innerHTML =
            '<i class="bi bi-check-circle me-2"></i>إضافة العامل';
        document.querySelector('#addWorkerModal .modal-footer .btn-primary').onclick = () => {
            this.addWorker();
        };
        document.getElementById('addWorkerForm').reset();
    }

    // توليد معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // تحميل التقارير
    loadReports() {
        // تحديث إحصائيات التقارير
        this.updateReportsStats();
    }

    // تحديث إحصائيات التقارير
    updateReportsStats() {
        // تحديث إحصائيات الخامات والتصنيفات
        document.getElementById('totalMaterials').textContent = this.materials.length;
        document.getElementById('totalCategories').textContent = this.categories.length;

        const activeMaterials = this.materials.filter(m => m.productCount > 0).length;
        const activeCategories = this.categories.filter(c => c.productCount > 0).length;

        document.getElementById('activeMaterials').textContent = activeMaterials;
        document.getElementById('activeCategories').textContent = activeCategories;
    }

    // إضافة مستخدم جديد
    addUser() {
        if (this.currentUser.role !== 'admin') {
            alert('ليس لديك صلاحية لإضافة مستخدمين');
            return;
        }

        const username = document.getElementById('newUsername').value;
        const fullName = document.getElementById('newUserFullName').value;
        const password = document.getElementById('newUserPassword').value;
        const role = document.getElementById('newUserRole').value;

        // جمع الصلاحيات
        const permissions = {
            products: document.getElementById('permProducts').checked,
            inventory: document.getElementById('permInventory').checked,
            workers: document.getElementById('permWorkers').checked,
            attendance: document.getElementById('permAttendance').checked,
            payroll: document.getElementById('permPayroll').checked,
            reports: document.getElementById('permReports').checked
        };

        if (this.users[username]) {
            alert('اسم المستخدم موجود بالفعل');
            return;
        }

        this.users[username] = {
            username,
            name: fullName,
            password,
            role,
            permissions,
            createdAt: new Date().toISOString()
        };

        this.saveUsers();
        this.loadUsersTable();

        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
        modal.hide();
        document.getElementById('addUserForm').reset();

        alert('تم إضافة المستخدم بنجاح');
    }

    // تحميل جدول المستخدمين
    loadUsersTable() {
        const tbody = document.getElementById('usersTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        Object.values(this.users).forEach(user => {
            if (user.username === 'admin') return; // لا نعرض المدير الرئيسي

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.username}</td>
                <td>
                    <span class="badge ${user.role === 'admin' ? 'bg-danger' : 'bg-primary'}">
                        ${user.role === 'admin' ? 'مدير' : 'مستخدم'}
                    </span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.deleteUser('${user.username}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // حذف مستخدم
    deleteUser(username) {
        if (this.currentUser.role !== 'admin') {
            alert('ليس لديك صلاحية لحذف المستخدمين');
            return;
        }

        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            delete this.users[username];
            this.saveUsers();
            this.loadUsersTable();
            alert('تم حذف المستخدم بنجاح');
        }
    }

    // تحديث إحصائيات المخزون
    updateInventoryStats() {
        const totalStockValue = this.products.reduce((sum, product) =>
            sum + (product.price * product.quantity), 0);
        const lowStockItems = this.products.filter(product => product.quantity < 10).length;
        const todayProduction = Math.floor(Math.random() * 100) + 50; // بيانات وهمية

        document.getElementById('totalStockValue').textContent = totalStockValue.toLocaleString() + ' ج.م';
        document.getElementById('lowStockItems').textContent = lowStockItems;
        document.getElementById('todayProduction').textContent = todayProduction;
    }

    // تحميل جدول المخزون
    loadInventoryTable() {
        const tbody = document.getElementById('inventoryTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        this.products.forEach(product => {
            const lastProduction = new Date().toLocaleDateString('ar-EG');
            const status = product.quantity === 0 ? 'نفد المخزون' :
                          product.quantity < 10 ? 'مخزون منخفض' : 'متوفر';
            const statusClass = product.quantity === 0 ? 'danger' :
                               product.quantity < 10 ? 'warning' : 'success';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.code}</td>
                <td>${product.name}</td>
                <td>${product.quantity}</td>
                <td>10</td>
                <td>${lastProduction}</td>
                <td>
                    <span class="badge bg-${statusClass}">${status}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="app.addStock('${product.id}')">
                        <i class="bi bi-plus-circle"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="app.removeStock('${product.id}')">
                        <i class="bi bi-dash-circle"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // إضافة كمية للمخزون
    addStock(productId) {
        const quantity = prompt('كم قطعة تريد إضافتها؟');
        if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
            const product = this.products.find(p => p.id === productId);
            if (product) {
                const previousQuantity = product.quantity;
                product.quantity += parseInt(quantity);
                product.totalPrice = product.price * product.quantity; // تحديث السعر الإجمالي

                // تسجيل الحركة في النظام المفصل
                this.recordInventoryMovement(
                    product,
                    'add',
                    parseInt(quantity),
                    previousQuantity,
                    'إضافة سريعة للمخزون',
                    'quick_add'
                );

                // حفظ العملية في سجل المخزون القديم للتوافق
                const today = new Date().toISOString().split('T')[0];
                if (!this.inventory[today]) {
                    this.inventory[today] = [];
                }

                this.inventory[today].push({
                    id: this.generateId(),
                    productId: product.id,
                    productName: product.name,
                    productCode: product.code,
                    operation: 'add',
                    quantity: parseInt(quantity),
                    notes: 'إضافة سريعة للمخزون',
                    date: today,
                    timestamp: new Date().toISOString(),
                    user: this.currentUser.name
                });

                this.saveInventory();
                this.saveProducts();
                this.loadInventoryTable();
                this.loadProductsTable();
                this.loadInventoryMovementTable(); // تحديث الجدول الجديد
                this.updateInventoryStats();
                this.updateDashboard();
                alert(`تم إضافة ${quantity} قطعة للمخزون`);
            }
        }
    }

    // خصم كمية من المخزون
    removeStock(productId) {
        const quantity = prompt('كم قطعة تريد خصمها؟');
        if (quantity && !isNaN(quantity) && parseInt(quantity) > 0) {
            const product = this.products.find(p => p.id === productId);
            if (product) {
                if (product.quantity >= parseInt(quantity)) {
                    const previousQuantity = product.quantity;
                    product.quantity -= parseInt(quantity);
                    product.totalPrice = product.price * product.quantity; // تحديث السعر الإجمالي

                    // تسجيل الحركة في النظام المفصل
                    this.recordInventoryMovement(
                        product,
                        'remove',
                        parseInt(quantity),
                        previousQuantity,
                        'خصم سريع من المخزون',
                        'quick_remove'
                    );

                    // حفظ العملية في سجل المخزون القديم للتوافق
                    const today = new Date().toISOString().split('T')[0];
                    if (!this.inventory[today]) {
                        this.inventory[today] = [];
                    }

                    this.inventory[today].push({
                        id: this.generateId(),
                        productId: product.id,
                        productName: product.name,
                        productCode: product.code,
                        operation: 'remove',
                        quantity: parseInt(quantity),
                        notes: 'خصم سريع من المخزون',
                        date: today,
                        timestamp: new Date().toISOString(),
                        user: this.currentUser.name
                    });

                    this.saveInventory();
                    this.saveProducts();
                    this.loadInventoryTable();
                    this.loadProductsTable();
                    this.loadInventoryMovementTable(); // تحديث الجدول الجديد
                    this.updateInventoryStats();
                    this.updateDashboard();
                    alert(`تم خصم ${quantity} قطعة من المخزون`);
                } else {
                    alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
                }
            }
        }
    }

    // معالجة عمليات المخزون
    processStockOperation() {
        const productId = document.getElementById('stockProductSelect').value;
        const quantity = parseInt(document.getElementById('stockQuantity').value);
        const operation = document.getElementById('stockOperation').value;
        const notes = document.getElementById('stockNotes').value;
        const operationDate = document.getElementById('stockDate').value;

        if (!productId || !quantity || quantity <= 0 || !operationDate) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        const product = this.products.find(p => p.id === productId);
        if (!product) {
            alert('المنتج غير موجود');
            return;
        }

        const previousQuantity = product.quantity;

        if (operation === 'add') {
            product.quantity += quantity;
            alert(`تم إضافة ${quantity} قطعة إلى ${product.name}`);
        } else if (operation === 'remove') {
            if (product.quantity < quantity) {
                alert('الكمية المطلوبة أكبر من المتوفر في المخزون');
                return;
            }
            product.quantity -= quantity;
            alert(`تم خصم ${quantity} قطعة من ${product.name}`);
        }

        // تحديث السعر الإجمالي
        product.totalPrice = product.price * product.quantity;

        // تسجيل الحركة في النظام المفصل
        this.recordInventoryMovement(
            product,
            operation,
            quantity,
            previousQuantity,
            notes,
            'manual'
        );

        // حفظ العملية في سجل المخزون مع التاريخ المحدد
        if (!this.inventory[operationDate]) {
            this.inventory[operationDate] = [];
        }

        this.inventory[operationDate].push({
            id: this.generateId(),
            productId: product.id,
            productName: product.name,
            productCode: product.code,
            operation: operation,
            quantity: quantity,
            notes: notes,
            date: operationDate,
            timestamp: new Date().toISOString(),
            user: this.currentUser.name
        });

        // حفظ البيانات
        this.saveProducts();
        this.saveInventory();

        // تحديث الواجهة
        this.updateDashboard();
        this.loadProductsTable();
        this.loadInventoryTable();
        this.loadInventoryMovementTable(); // تحديث الجدول الجديد
        this.updateInventoryStats();

        // إغلاق النافذة وإعادة تعيين النموذج
        const modal = bootstrap.Modal.getInstance(document.getElementById('addStockModal'));
        modal.hide();
        document.getElementById('addStockForm').reset();
    }

    // تحميل قائمة العمال في نافذة سحب المخزون
    loadWorkersForWithdrawal() {
        const workerSelect = document.getElementById('withdrawalWorkerSelect');
        if (!workerSelect) return;

        workerSelect.innerHTML = '<option value="">اختر العامل</option>';

        this.workers.filter(worker => worker.status === 'active').forEach(worker => {
            const option = document.createElement('option');
            option.value = worker.id;
            option.textContent = `${worker.name} - ${worker.job}`;
            workerSelect.appendChild(option);
        });
    }

    // تحميل قائمة المنتجات في نافذة سحب المخزون
    loadProductsForWithdrawal() {
        const productSelect = document.getElementById('withdrawalProductSelect');
        if (!productSelect) return;

        productSelect.innerHTML = '<option value="">اختر المنتج</option>';

        this.products.filter(product => product.quantity > 0).forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - متوفر: ${product.quantity}`;
            productSelect.appendChild(option);
        });
    }

    // معالجة سحب العامل من المخزون
    processWorkerWithdrawal() {
        const workerId = document.getElementById('withdrawalWorkerSelect').value;
        const productId = document.getElementById('withdrawalProductSelect').value;
        const quantity = parseInt(document.getElementById('withdrawalQuantity').value);
        const notes = document.getElementById('withdrawalNotes').value.trim();

        // التحقق من صحة البيانات
        if (!workerId) {
            this.showError('يرجى اختيار العامل');
            return;
        }

        if (!productId) {
            this.showError('يرجى اختيار المنتج');
            return;
        }

        if (!quantity || quantity <= 0) {
            this.showError('يرجى إدخال كمية صحيحة');
            return;
        }

        const worker = this.workers.find(w => w.id === workerId);
        const product = this.products.find(p => p.id === productId);

        if (!worker || !product) {
            this.showError('العامل أو المنتج غير موجود');
            return;
        }

        if (product.quantity < quantity) {
            this.showError(`الكمية المطلوبة (${quantity}) أكبر من المتوفر في المخزون (${product.quantity})`);
            return;
        }

        // تأكيد العملية
        this.showConfirm(
            `هل أنت متأكد من سحب ${quantity} من ${product.name} للعامل ${worker.name}؟`,
            () => {
                this.executeWorkerWithdrawal(worker, product, quantity, notes);
            }
        );
    }

    // تنفيذ عملية السحب
    executeWorkerWithdrawal(worker, product, quantity, notes) {
        try {
            // خصم الكمية من المخزون
            const previousQuantity = product.quantity;
            product.quantity -= quantity;
            product.totalPrice = product.price * product.quantity;

            // تسجيل الحركة في النظام المفصل
            this.recordInventoryMovement(
                product,
                'worker_withdrawal',
                quantity,
                previousQuantity,
                `سحب بواسطة العامل: ${worker.name} - ${notes}`,
                'worker_withdrawal'
            );

            // إنشاء سجل السحب
            const withdrawalRecord = {
                id: this.generateId(),
                workerId: worker.id,
                workerName: worker.name,
                workerJob: worker.job,
                productId: product.id,
                productName: product.name,
                productCode: product.code,
                quantity: quantity,
                unitPrice: product.price,
                totalValue: product.price * quantity,
                notes: notes,
                date: new Date().toISOString().split('T')[0],
                time: new Date().toLocaleTimeString('en-GB', {hour12: false}),
                timestamp: new Date().toISOString()
            };

            // حفظ السجل في قاعدة البيانات
            if (!this.workerWithdrawals) {
                this.workerWithdrawals = [];
            }
            this.workerWithdrawals.push(withdrawalRecord);

            // تحديث سجل المخزون العام
            const today = new Date().toISOString().split('T')[0];
            if (!this.inventory[today]) {
                this.inventory[today] = [];
            }

            this.inventory[today].push({
                productId: product.id,
                productName: product.name,
                operation: 'worker_withdrawal',
                quantity: quantity,
                notes: `سحب بواسطة العامل: ${worker.name}`,
                timestamp: new Date().toISOString(),
                user: this.currentUser.name,
                workerId: worker.id,
                workerName: worker.name
            });

            // حفظ البيانات
            this.saveProducts();
            this.saveInventory();
            this.saveWorkerWithdrawals();

            // تحديث الواجهة
            this.updateDashboard();
            this.loadProductsTable();
            this.loadInventoryTable();
            this.loadWorkerWithdrawalsTable();
            this.loadInventoryMovementTable(); // تحديث الجدول الجديد
            this.updateInventoryStats();

            // إغلاق النافذة وإعادة تعيين النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('workerWithdrawalModal'));
            modal.hide();
            document.getElementById('workerWithdrawalForm').reset();

            this.showSuccess(`تم سحب ${quantity} من ${product.name} للعامل ${worker.name} بنجاح`);

            // عرض خيار طباعة التقرير
            setTimeout(() => {
                this.showConfirm(
                    'هل تريد طباعة تقرير السحب الآن؟',
                    () => {
                        this.printWorkerWithdrawalReport(withdrawalRecord.id);
                    }
                );
            }, 1000);

        } catch (error) {
            console.error('خطأ في عملية السحب:', error);
            this.showError('حدث خطأ أثناء عملية السحب. يرجى المحاولة مرة أخرى.');
        }
    }

    // حفظ سجلات سحب العمال
    saveWorkerWithdrawals() {
        localStorage.setItem('bagFactory_workerWithdrawals', JSON.stringify(this.workerWithdrawals || []));
    }

    // تحميل سجلات سحب العمال
    loadWorkerWithdrawals() {
        const saved = localStorage.getItem('bagFactory_workerWithdrawals');
        return saved ? JSON.parse(saved) : [];
    }

    // تحميل سجلات حركة المخزون المفصلة
    loadInventoryMovements() {
        const saved = localStorage.getItem('bagFactory_inventoryMovements');
        return saved ? JSON.parse(saved) : [];
    }

    // حفظ سجلات حركة المخزون المفصلة
    saveInventoryMovements() {
        localStorage.setItem('bagFactory_inventoryMovements', JSON.stringify(this.inventoryMovements || []));
    }

    // تسجيل حركة مخزون مفصلة
    recordInventoryMovement(product, operation, quantity, previousQuantity, notes = '', operationType = 'manual') {
        const movement = {
            id: this.generateId(),
            productId: product.id,
            productName: product.name,
            productCode: product.code,
            operation: operation, // 'add', 'remove', 'worker_withdrawal'
            operationType: operationType, // 'manual', 'worker_withdrawal', 'quick_add', 'quick_remove'
            quantity: quantity,
            previousQuantity: previousQuantity,
            currentQuantity: product.quantity,
            date: new Date().toISOString().split('T')[0],
            time: new Date().toLocaleTimeString('ar-EG', {hour12: false}),
            timestamp: new Date().toISOString(),
            user: this.currentUser ? this.currentUser.name : 'النظام',
            notes: notes || ''
        };

        if (!this.inventoryMovements) {
            this.inventoryMovements = [];
        }

        this.inventoryMovements.push(movement);
        this.saveInventoryMovements();

        return movement;
    }

    // تحميل جدول حركة المخزون المفصل
    loadInventoryMovementTable() {
        const tbody = document.getElementById('inventoryMovementTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        // ترتيب الحركات حسب التاريخ والوقت (الأحدث أولاً)
        const sortedMovements = [...(this.inventoryMovements || [])].sort((a, b) => {
            return new Date(b.timestamp) - new Date(a.timestamp);
        });

        sortedMovements.forEach(movement => {
            const row = document.createElement('tr');

            // تحديد لون العملية
            let operationClass = '';
            let operationText = '';
            switch(movement.operation) {
                case 'add':
                    operationClass = 'text-success';
                    operationText = 'إضافة';
                    break;
                case 'remove':
                    operationClass = 'text-danger';
                    operationText = 'سحب';
                    break;
                case 'worker_withdrawal':
                    operationClass = 'text-warning';
                    operationText = 'سحب عامل';
                    break;
                default:
                    operationClass = 'text-secondary';
                    operationText = movement.operation;
            }

            row.innerHTML = `
                <td>${movement.date}</td>
                <td>${movement.time}</td>
                <td><strong>${movement.productCode}</strong></td>
                <td>${movement.productName}</td>
                <td><span class="${operationClass}"><strong>${operationText}</strong></span></td>
                <td>${movement.previousQuantity}</td>
                <td class="${operationClass}"><strong>${movement.operation === 'add' ? '+' : '-'}${movement.quantity}</strong></td>
                <td><strong>${movement.currentQuantity}</strong></td>
                <td>${movement.user}</td>
                <td>${movement.notes || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="app.viewInventoryMovementDetails('${movement.id}')">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // إظهار رسالة إذا لم توجد بيانات
        if (sortedMovements.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>لا توجد حركات مخزون مسجلة
                </td>
            `;
            tbody.appendChild(row);
        }
    }

    // فلترة حركات المخزون
    applyInventoryMovementFilters() {
        const searchTerm = document.getElementById('inventoryMovementSearch').value.toLowerCase();
        const dateFrom = document.getElementById('inventoryMovementDateFrom').value;
        const dateTo = document.getElementById('inventoryMovementDateTo').value;
        const typeFilter = document.getElementById('inventoryMovementTypeFilter').value;

        let filteredMovements = [...(this.inventoryMovements || [])];

        // فلترة بكود المنتج أو اسم المنتج
        if (searchTerm) {
            filteredMovements = filteredMovements.filter(movement =>
                movement.productCode.toLowerCase().includes(searchTerm) ||
                movement.productName.toLowerCase().includes(searchTerm)
            );
        }

        // فلترة بالتاريخ من
        if (dateFrom) {
            filteredMovements = filteredMovements.filter(movement =>
                movement.date >= dateFrom
            );
        }

        // فلترة بالتاريخ إلى
        if (dateTo) {
            filteredMovements = filteredMovements.filter(movement =>
                movement.date <= dateTo
            );
        }

        // فلترة بنوع العملية
        if (typeFilter) {
            filteredMovements = filteredMovements.filter(movement =>
                movement.operation === typeFilter
            );
        }

        // عرض النتائج المفلترة
        this.displayFilteredInventoryMovements(filteredMovements);
    }

    // عرض حركات المخزون المفلترة
    displayFilteredInventoryMovements(movements) {
        const tbody = document.getElementById('inventoryMovementTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        // ترتيب الحركات حسب التاريخ والوقت (الأحدث أولاً)
        const sortedMovements = movements.sort((a, b) => {
            return new Date(b.timestamp) - new Date(a.timestamp);
        });

        sortedMovements.forEach(movement => {
            const row = document.createElement('tr');

            // تحديد لون العملية
            let operationClass = '';
            let operationText = '';
            switch(movement.operation) {
                case 'add':
                    operationClass = 'text-success';
                    operationText = 'إضافة';
                    break;
                case 'remove':
                    operationClass = 'text-danger';
                    operationText = 'سحب';
                    break;
                case 'worker_withdrawal':
                    operationClass = 'text-warning';
                    operationText = 'سحب عامل';
                    break;
                default:
                    operationClass = 'text-secondary';
                    operationText = movement.operation;
            }

            row.innerHTML = `
                <td>${movement.date}</td>
                <td>${movement.time}</td>
                <td><strong>${movement.productCode}</strong></td>
                <td>${movement.productName}</td>
                <td><span class="${operationClass}"><strong>${operationText}</strong></span></td>
                <td>${movement.previousQuantity}</td>
                <td class="${operationClass}"><strong>${movement.operation === 'add' ? '+' : '-'}${movement.quantity}</strong></td>
                <td><strong>${movement.currentQuantity}</strong></td>
                <td>${movement.user}</td>
                <td>${movement.notes || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-info" onclick="app.viewInventoryMovementDetails('${movement.id}')">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // إظهار رسالة إذا لم توجد بيانات
        if (sortedMovements.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>لا توجد نتائج تطابق البحث
                </td>
            `;
            tbody.appendChild(row);
        }
    }

    // مسح فلاتر حركة المخزون
    clearInventoryMovementFilters() {
        document.getElementById('inventoryMovementSearch').value = '';
        document.getElementById('inventoryMovementDateFrom').value = '';
        document.getElementById('inventoryMovementDateTo').value = '';
        document.getElementById('inventoryMovementTypeFilter').value = '';
        this.loadInventoryMovementTable();
    }

    // عرض تفاصيل حركة مخزون
    viewInventoryMovementDetails(movementId) {
        const movement = this.inventoryMovements.find(m => m.id === movementId);
        if (!movement) {
            alert('لم يتم العثور على تفاصيل الحركة');
            return;
        }

        let operationText = '';
        switch(movement.operation) {
            case 'add':
                operationText = 'إضافة للمخزون';
                break;
            case 'remove':
                operationText = 'سحب من المخزون';
                break;
            case 'worker_withdrawal':
                operationText = 'سحب بواسطة عامل';
                break;
            default:
                operationText = movement.operation;
        }

        const details = `
تفاصيل حركة المخزون:

📅 التاريخ: ${movement.date}
🕐 الوقت: ${movement.time}
🏷️ كود المنتج: ${movement.productCode}
📦 اسم المنتج: ${movement.productName}
⚡ نوع العملية: ${operationText}
📊 الكمية السابقة: ${movement.previousQuantity}
🔢 الكمية المضافة/المسحوبة: ${movement.quantity}
📈 الكمية الحالية: ${movement.currentQuantity}
👤 المستخدم: ${movement.user}
📝 ملاحظات: ${movement.notes || 'لا توجد ملاحظات'}
        `;

        alert(details);
    }

    // طباعة تقرير حركة المخزون
    printInventoryMovementReport() {
        const searchTerm = document.getElementById('inventoryMovementSearch').value;
        const dateFrom = document.getElementById('inventoryMovementDateFrom').value;
        const dateTo = document.getElementById('inventoryMovementDateTo').value;
        const typeFilter = document.getElementById('inventoryMovementTypeFilter').value;

        // تطبيق الفلاتر للحصول على البيانات المطلوبة
        let movements = [...(this.inventoryMovements || [])];

        if (searchTerm) {
            movements = movements.filter(movement =>
                movement.productCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                movement.productName.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (dateFrom) {
            movements = movements.filter(movement => movement.date >= dateFrom);
        }

        if (dateTo) {
            movements = movements.filter(movement => movement.date <= dateTo);
        }

        if (typeFilter) {
            movements = movements.filter(movement => movement.operation === typeFilter);
        }

        // ترتيب حسب التاريخ
        movements.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // إنشاء محتوى التقرير
        let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حركة المخزون الزمني</title>
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .company-name { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
                .report-title { font-size: 20px; color: #34495e; margin-bottom: 5px; }
                .report-date { font-size: 14px; color: #7f8c8d; }
                .filters { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
                .filters h4 { margin-top: 0; color: #495057; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; font-weight: bold; }
                .add { color: #28a745; font-weight: bold; }
                .remove { color: #dc3545; font-weight: bold; }
                .worker_withdrawal { color: #ffc107; font-weight: bold; }
                .summary { margin-top: 30px; background: #e9ecef; padding: 15px; border-radius: 5px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">مصنع الشنط</div>
                <div class="report-title">تقرير حركة المخزون الزمني</div>
                <div class="report-date">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')} - ${new Date().toLocaleTimeString('ar-EG')}</div>
            </div>
        `;

        // إضافة معلومات الفلاتر المطبقة
        if (searchTerm || dateFrom || dateTo || typeFilter) {
            reportContent += `
            <div class="filters">
                <h4>الفلاتر المطبقة:</h4>
                ${searchTerm ? `<p><strong>البحث:</strong> ${searchTerm}</p>` : ''}
                ${dateFrom ? `<p><strong>من تاريخ:</strong> ${dateFrom}</p>` : ''}
                ${dateTo ? `<p><strong>إلى تاريخ:</strong> ${dateTo}</p>` : ''}
                ${typeFilter ? `<p><strong>نوع العملية:</strong> ${this.getOperationText(typeFilter)}</p>` : ''}
            </div>
            `;
        }

        reportContent += `
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>كود المنتج</th>
                        <th>اسم المنتج</th>
                        <th>نوع العملية</th>
                        <th>الكمية السابقة</th>
                        <th>الكمية المضافة/المسحوبة</th>
                        <th>الكمية الحالية</th>
                        <th>المستخدم</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
        `;

        movements.forEach(movement => {
            const operationClass = movement.operation === 'add' ? 'add' :
                                 movement.operation === 'remove' ? 'remove' : 'worker_withdrawal';
            const operationText = this.getOperationText(movement.operation);
            const quantitySign = movement.operation === 'add' ? '+' : '-';

            reportContent += `
                <tr>
                    <td>${movement.date}</td>
                    <td>${movement.time}</td>
                    <td><strong>${movement.productCode}</strong></td>
                    <td>${movement.productName}</td>
                    <td class="${operationClass}">${operationText}</td>
                    <td>${movement.previousQuantity}</td>
                    <td class="${operationClass}"><strong>${quantitySign}${movement.quantity}</strong></td>
                    <td><strong>${movement.currentQuantity}</strong></td>
                    <td>${movement.user}</td>
                    <td>${movement.notes || '-'}</td>
                </tr>
            `;
        });

        reportContent += `
                </tbody>
            </table>
        `;

        // إضافة ملخص إحصائي
        const totalAdd = movements.filter(m => m.operation === 'add').reduce((sum, m) => sum + m.quantity, 0);
        const totalRemove = movements.filter(m => m.operation === 'remove').reduce((sum, m) => sum + m.quantity, 0);
        const totalWorkerWithdrawal = movements.filter(m => m.operation === 'worker_withdrawal').reduce((sum, m) => sum + m.quantity, 0);

        reportContent += `
            <div class="summary">
                <h4>ملخص إحصائي:</h4>
                <p><strong>إجمالي عدد الحركات:</strong> ${movements.length}</p>
                <p><strong>إجمالي الكميات المضافة:</strong> <span class="add">${totalAdd}</span></p>
                <p><strong>إجمالي الكميات المسحوبة:</strong> <span class="remove">${totalRemove}</span></p>
                <p><strong>إجمالي سحب العمال:</strong> <span class="worker_withdrawal">${totalWorkerWithdrawal}</span></p>
                <p><strong>صافي الحركة:</strong> ${totalAdd - totalRemove - totalWorkerWithdrawal}</p>
            </div>
        `;

        reportContent += `
        </body>
        </html>
        `;

        // فتح نافذة الطباعة
        const reportWindow = window.open('', '_blank');
        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }

    // الحصول على نص العملية
    getOperationText(operation) {
        switch(operation) {
            case 'add':
                return 'إضافة';
            case 'remove':
                return 'سحب';
            case 'worker_withdrawal':
                return 'سحب عامل';
            default:
                return operation;
        }
    }

    // عرض نافذة سحب العمال من المخزون
    showWorkerWithdrawalModal() {
        this.loadWorkersForWithdrawal();
        this.loadProductsForWithdrawal();
        const modal = new bootstrap.Modal(document.getElementById('workerWithdrawalModal'));
        modal.show();
    }

    // تحميل جدول سجلات سحب العمال
    loadWorkerWithdrawalsTable() {
        const tbody = document.getElementById('workerWithdrawalsTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (!this.workerWithdrawals || this.workerWithdrawals.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-inbox me-2"></i>لا توجد سجلات سحب حتى الآن
                </td>
            `;
            tbody.appendChild(row);
            return;
        }

        // ترتيب السجلات حسب التاريخ (الأحدث أولاً)
        const sortedWithdrawals = [...this.workerWithdrawals].sort((a, b) =>
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        sortedWithdrawals.forEach(record => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(record.date).toLocaleDateString('ar-EG')}</td>
                <td>${record.time}</td>
                <td>${record.workerName}</td>
                <td>${record.workerJob}</td>
                <td>${record.productName} (${record.productCode})</td>
                <td>
                    <span class="badge bg-warning">${record.quantity}</span>
                </td>
                <td>${record.totalValue.toLocaleString()} ج.م</td>
                <td>${record.notes || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.printWorkerWithdrawalReport('${record.id}')" title="طباعة التقرير">
                        <i class="bi bi-printer"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="app.viewWithdrawalDetails('${record.id}')" title="عرض التفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // تحديث فلتر العمال
        this.updateWithdrawalWorkerFilter();
    }

    // تحديث فلتر العمال في سجلات السحب
    updateWithdrawalWorkerFilter() {
        const workerFilter = document.getElementById('withdrawalWorkerFilter');
        if (!workerFilter) return;

        // الحصول على قائمة العمال الذين لديهم سجلات سحب
        const workersWithWithdrawals = [...new Set(this.workerWithdrawals.map(w => w.workerId))];
        const workers = this.workers.filter(worker => workersWithWithdrawals.includes(worker.id));

        workerFilter.innerHTML = '<option value="">جميع العمال</option>';
        workers.forEach(worker => {
            const option = document.createElement('option');
            option.value = worker.id;
            option.textContent = worker.name;
            workerFilter.appendChild(option);
        });
    }

    // عرض تفاصيل عملية السحب
    viewWithdrawalDetails(withdrawalId) {
        const withdrawal = this.workerWithdrawals.find(w => w.id === withdrawalId);
        if (!withdrawal) {
            this.showError('سجل السحب غير موجود');
            return;
        }

        const details = `
            <div class="withdrawal-details">
                <h6><i class="bi bi-info-circle me-2"></i>تفاصيل عملية السحب</h6>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>التاريخ:</strong> ${new Date(withdrawal.date).toLocaleDateString('ar-EG')}</p>
                        <p><strong>الوقت:</strong> ${withdrawal.time}</p>
                        <p><strong>اسم العامل:</strong> ${withdrawal.workerName}</p>
                        <p><strong>الوظيفة:</strong> ${withdrawal.workerJob}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>المنتج:</strong> ${withdrawal.productName}</p>
                        <p><strong>كود المنتج:</strong> ${withdrawal.productCode}</p>
                        <p><strong>الكمية:</strong> ${withdrawal.quantity}</p>
                        <p><strong>القيمة الإجمالية:</strong> ${withdrawal.totalValue.toLocaleString()} ج.م</p>
                    </div>
                </div>
                ${withdrawal.notes ? `<p><strong>ملاحظات:</strong> ${withdrawal.notes}</p>` : ''}
            </div>
        `;

        // عرض التفاصيل في نافذة منبثقة
        this.showInfo('تفاصيل عملية السحب', details);
    }

    // طباعة تقرير سحب عامل محدد
    printWorkerWithdrawalReport(withdrawalId) {
        const withdrawal = this.workerWithdrawals.find(w => w.id === withdrawalId);
        if (!withdrawal) {
            this.showError('سجل السحب غير موجود');
            return;
        }

        const reportWindow = window.open('', '_blank');
        const reportContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير سحب عامل من المخزون</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .details { margin: 20px 0; }
                .details table { width: 100%; border-collapse: collapse; }
                .details th, .details td { padding: 10px; border: 1px solid #ddd; text-align: right; }
                .details th { background-color: #f5f5f5; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>تقرير سحب عامل من المخزون</h2>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
            </div>

            <div class="details">
                <table>
                    <tr><th>التاريخ</th><td>${new Date(withdrawal.date).toLocaleDateString('ar-EG')}</td></tr>
                    <tr><th>الوقت</th><td>${withdrawal.time}</td></tr>
                    <tr><th>اسم العامل</th><td>${withdrawal.workerName}</td></tr>
                    <tr><th>الوظيفة</th><td>${withdrawal.workerJob}</td></tr>
                    <tr><th>المنتج</th><td>${withdrawal.productName}</td></tr>
                    <tr><th>كود المنتج</th><td>${withdrawal.productCode}</td></tr>
                    <tr><th>الكمية المسحوبة</th><td>${withdrawal.quantity}</td></tr>
                    <tr><th>سعر الوحدة</th><td>${withdrawal.unitPrice.toLocaleString()} ج.م</td></tr>
                    <tr><th>القيمة الإجمالية</th><td>${withdrawal.totalValue.toLocaleString()} ج.م</td></tr>
                    ${withdrawal.notes ? `<tr><th>ملاحظات</th><td>${withdrawal.notes}</td></tr>` : ''}
                </table>
            </div>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }

    // طباعة تقرير شامل لسجلات سحب العمال
    printWorkerWithdrawalsReport() {
        if (!this.workerWithdrawals || this.workerWithdrawals.length === 0) {
            this.showWarning('لا توجد سجلات سحب لطباعة التقرير');
            return;
        }

        const reportWindow = window.open('', '_blank');
        const totalValue = this.workerWithdrawals.reduce((sum, w) => sum + w.totalValue, 0);
        const totalQuantity = this.workerWithdrawals.reduce((sum, w) => sum + w.quantity, 0);

        const reportContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير شامل - سجلات سحب العمال من المخزون</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .summary { margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }
                .summary h4 { margin-bottom: 15px; color: #495057; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { padding: 8px; border: 1px solid #ddd; text-align: right; font-size: 12px; }
                th { background-color: #f5f5f5; font-weight: bold; }
                .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>تقرير شامل - سجلات سحب العمال من المخزون</h2>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                <p>عدد السجلات: ${this.workerWithdrawals.length}</p>
            </div>

            <div class="summary">
                <h4>ملخص الإحصائيات</h4>
                <div style="display: flex; justify-content: space-around;">
                    <div><strong>إجمالي الكمية المسحوبة:</strong> ${totalQuantity.toLocaleString()}</div>
                    <div><strong>إجمالي القيمة:</strong> ${totalValue.toLocaleString()} ج.م</div>
                    <div><strong>عدد العمال:</strong> ${[...new Set(this.workerWithdrawals.map(w => w.workerId))].length}</div>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>اسم العامل</th>
                        <th>الوظيفة</th>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>القيمة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.workerWithdrawals.map(withdrawal => `
                        <tr>
                            <td>${new Date(withdrawal.date).toLocaleDateString('ar-EG')}</td>
                            <td>${withdrawal.time}</td>
                            <td>${withdrawal.workerName}</td>
                            <td>${withdrawal.workerJob}</td>
                            <td>${withdrawal.productName}</td>
                            <td>${withdrawal.quantity}</td>
                            <td>${withdrawal.totalValue.toLocaleString()} ج.م</td>
                            <td>${withdrawal.notes || '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }

    // تطبيق جميع فلاتر سجلات سحب العمال
    applyWithdrawalFilters() {
        if (!this.workerWithdrawals || this.workerWithdrawals.length === 0) return;

        const searchTerm = document.getElementById('withdrawalSearch').value.toLowerCase();
        const dateFilter = document.getElementById('withdrawalDateFilter').value;
        const workerFilter = document.getElementById('withdrawalWorkerFilter').value;

        let filteredWithdrawals = [...this.workerWithdrawals];

        // تطبيق فلتر البحث النصي
        if (searchTerm) {
            filteredWithdrawals = filteredWithdrawals.filter(withdrawal =>
                withdrawal.workerName.toLowerCase().includes(searchTerm) ||
                withdrawal.productName.toLowerCase().includes(searchTerm) ||
                withdrawal.productCode.toLowerCase().includes(searchTerm) ||
                withdrawal.notes.toLowerCase().includes(searchTerm)
            );
        }

        // تطبيق فلتر التاريخ
        if (dateFilter) {
            filteredWithdrawals = filteredWithdrawals.filter(withdrawal =>
                withdrawal.date === dateFilter
            );
        }

        // تطبيق فلتر العامل
        if (workerFilter) {
            filteredWithdrawals = filteredWithdrawals.filter(withdrawal =>
                withdrawal.workerId === workerFilter
            );
        }

        this.displayFilteredWithdrawals(filteredWithdrawals);
    }

    // مسح جميع الفلاتر
    clearWithdrawalFilters() {
        document.getElementById('withdrawalSearch').value = '';
        document.getElementById('withdrawalDateFilter').value = '';
        document.getElementById('withdrawalWorkerFilter').value = '';
        this.loadWorkerWithdrawalsTable();
    }

    // إعادة ضبط المصنع
    factoryReset() {
        // مسح جميع البيانات من localStorage
        const keysToRemove = [
            'bagFactory_products',
            'bagFactory_workers',
            'bagFactory_attendance',
            'bagFactory_inventory',
            'bagFactory_payroll',
            'bagFactory_materials',
            'bagFactory_categories',
            'bagFactory_workerWithdrawals',
            'bagFactory_inventoryMovements',
            'bagFactory_currentUser'
        ];

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
        });

        // إعادة تعيين المستخدمين للمدير الرئيسي فقط
        const defaultUsers = {
            'admin': {
                username: 'admin',
                password: 'admin123',
                role: 'admin',
                name: 'المدير العام'
            }
        };
        localStorage.setItem('bagFactory_users', JSON.stringify(defaultUsers));

        // إعادة تحميل الصفحة
        alert('تم إعادة ضبط المصنع بنجاح. سيتم إعادة تحميل الصفحة.');
        window.location.reload();
    }

    // البحث في سجلات سحب العمال (للتوافق مع الإصدارات السابقة)
    searchWorkerWithdrawals(searchTerm) {
        document.getElementById('withdrawalSearch').value = searchTerm;
        this.applyWithdrawalFilters();
    }

    // فلترة سجلات سحب العمال حسب العامل (للتوافق مع الإصدارات السابقة)
    filterWorkerWithdrawals(workerId) {
        document.getElementById('withdrawalWorkerFilter').value = workerId;
        this.applyWithdrawalFilters();
    }

    // عرض سجلات السحب المفلترة
    displayFilteredWithdrawals(withdrawals) {
        const tbody = document.getElementById('workerWithdrawalsTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (withdrawals.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="9" class="text-center text-muted">
                    <i class="bi bi-search me-2"></i>لا توجد نتائج مطابقة للبحث
                </td>
            `;
            tbody.appendChild(row);
            return;
        }

        // ترتيب السجلات حسب التاريخ (الأحدث أولاً)
        const sortedWithdrawals = [...withdrawals].sort((a, b) =>
            new Date(b.timestamp) - new Date(a.timestamp)
        );

        sortedWithdrawals.forEach(record => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(record.date).toLocaleDateString('ar-EG')}</td>
                <td>${record.time}</td>
                <td>${record.workerName}</td>
                <td>${record.workerJob}</td>
                <td>${record.productName} (${record.productCode})</td>
                <td>
                    <span class="badge bg-warning">${record.quantity}</span>
                </td>
                <td>${record.totalValue.toLocaleString()} ج.م</td>
                <td>${record.notes || '-'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="app.printWorkerWithdrawalReport('${record.id}')" title="طباعة التقرير">
                        <i class="bi bi-printer"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="app.viewWithdrawalDetails('${record.id}')" title="عرض التفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // تسجيل حضور جميع العمال
    markAllPresent() {
        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];

        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        this.workers.forEach(worker => {
            if (worker.status === 'active') {
                this.attendance[date][worker.id] = {
                    status: 'present',
                    arrivalTime: new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5),
                    leaveTime: '',
                    notes: ''
                };
            }
        });

        this.saveAttendance();
        this.loadAttendanceTable();
        alert('تم تسجيل حضور جميع العمال');
    }

    // تسجيل انصراف جميع العمال
    markAllLeave() {
        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        const currentTime = new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5);

        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        this.workers.forEach(worker => {
            if (worker.status === 'active') {
                if (!this.attendance[date][worker.id]) {
                    this.attendance[date][worker.id] = {
                        status: 'present',
                        arrivalTime: '08:00',
                        leaveTime: currentTime,
                        notes: ''
                    };
                } else {
                    this.attendance[date][worker.id].leaveTime = currentTime;
                }
            }
        });

        this.saveAttendance();
        this.loadAttendanceTable();
        alert('تم تسجيل انصراف جميع العمال');
    }

    // تحميل جدول الحضور
    loadAttendanceTable() {
        const tbody = document.getElementById('attendanceTable');
        if (!tbody) return;

        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        tbody.innerHTML = '';

        let todayPresent = 0, todayAbsent = 0, todayLate = 0, todayLeft = 0;

        this.workers.forEach(worker => {
            if (worker.status !== 'active') return;

            const attendance = this.attendance[date] && this.attendance[date][worker.id];
            const status = attendance ? attendance.status : 'absent';
            const arrivalTime = attendance ? attendance.arrivalTime : '';
            const leaveTime = attendance ? attendance.leaveTime : '';
            const notes = attendance ? attendance.notes : '';

            // حساب ساعات العمل
            let workingHours = '';
            if (arrivalTime && leaveTime) {
                const arrival = new Date(`${date}T${arrivalTime}`);
                const leave = new Date(`${date}T${leaveTime}`);
                const diffMs = leave - arrival;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                workingHours = `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
            }

            // إحصائيات اليوم
            if (status === 'present') todayPresent++;
            else if (status === 'absent') todayAbsent++;
            else if (status === 'late') todayLate++;
            if (leaveTime) todayLeft++;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${worker.name}</td>
                <td>${worker.job}</td>
                <td>
                    <select class="form-select form-select-sm" onchange="app.updateAttendance('${worker.id}', '${date}', this.value)">
                        <option value="present" ${status === 'present' ? 'selected' : ''}>حاضر</option>
                        <option value="absent" ${status === 'absent' ? 'selected' : ''}>غائب</option>
                        <option value="late" ${status === 'late' ? 'selected' : ''}>متأخر</option>
                    </select>
                </td>
                <td>
                    <input type="time" class="form-control form-control-sm" value="${arrivalTime}"
                           onchange="app.updateArrivalTime('${worker.id}', '${date}', this.value)">
                </td>
                <td>
                    <input type="time" class="form-control form-control-sm" value="${leaveTime}"
                           onchange="app.updateLeaveTime('${worker.id}', '${date}', this.value)">
                </td>
                <td><strong>${workingHours}</strong></td>
                <td>
                    <input type="text" class="form-control form-control-sm" value="${notes}"
                           onchange="app.updateAttendanceNotes('${worker.id}', '${date}', this.value)">
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-success me-1" onclick="app.markPresent('${worker.id}', '${date}')">
                        <i class="bi bi-check"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="app.markLeave('${worker.id}', '${date}')">
                        <i class="bi bi-door-open"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // تحديث الإحصائيات
        if (document.getElementById('todayPresent')) {
            document.getElementById('todayPresent').textContent = todayPresent;
            document.getElementById('todayAbsent').textContent = todayAbsent;
            document.getElementById('todayLate').textContent = todayLate;
            document.getElementById('todayLeft').textContent = todayLeft;
        }
    }

    // تحديث حالة الحضور
    updateAttendance(workerId, date, status) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].status = status;
        this.saveAttendance();
        this.loadAttendanceTable();
    }

    // تحديث وقت الحضور
    updateArrivalTime(workerId, date, time) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].arrivalTime = time;
        this.saveAttendance();
        this.loadAttendanceTable();
    }

    // تحديث وقت الانصراف
    updateLeaveTime(workerId, date, time) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].leaveTime = time;
        this.saveAttendance();
        this.loadAttendanceTable();
    }

    // تحديث ملاحظات الحضور
    updateAttendanceNotes(workerId, date, notes) {
        if (!this.attendance[date]) {
            this.attendance[date] = {};
        }

        if (!this.attendance[date][workerId]) {
            this.attendance[date][workerId] = {};
        }

        this.attendance[date][workerId].notes = notes;
        this.saveAttendance();
    }

    // تسجيل حضور عامل
    markPresent(workerId, date) {
        this.updateAttendance(workerId, date, 'present');
        this.updateArrivalTime(workerId, date, new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5));
    }

    // تسجيل انصراف عامل
    markLeave(workerId, date) {
        this.updateLeaveTime(workerId, date, new Date().toLocaleTimeString('en-GB', {hour12: false}).slice(0, 5));
    }

    // حساب المرتبات
    calculatePayroll() {
        const periodType = document.getElementById('payrollPeriodType').value;
        let startDate, endDate, periodKey;

        // تحديد الفترة حسب النوع
        switch(periodType) {
            case 'daily':
                startDate = endDate = document.getElementById('payrollStartDate').value || new Date().toISOString().split('T')[0];
                periodKey = `daily_${startDate}`;
                break;
            case 'weekly':
                startDate = document.getElementById('payrollStartDate').value;
                if (!startDate) {
                    const today = new Date();
                    const dayOfWeek = today.getDay();
                    const startOfWeek = new Date(today);
                    startOfWeek.setDate(today.getDate() - dayOfWeek);
                    startDate = startOfWeek.toISOString().split('T')[0];
                }
                const weekEnd = new Date(startDate);
                weekEnd.setDate(weekEnd.getDate() + 6);
                endDate = weekEnd.toISOString().split('T')[0];
                periodKey = `weekly_${startDate}`;
                break;
            case 'monthly':
                const month = document.getElementById('payrollMonth').value;
                if (!month) {
                    alert('يرجى اختيار الشهر');
                    return;
                }
                const [year, monthNum] = month.split('-');
                startDate = `${year}-${monthNum}-01`;
                endDate = new Date(year, monthNum, 0).toISOString().split('T')[0];
                periodKey = month;
                break;
            case 'yearly':
                const selectedYear = document.getElementById('payrollYear').value;
                if (!selectedYear) {
                    alert('يرجى اختيار السنة');
                    return;
                }
                startDate = `${selectedYear}-01-01`;
                endDate = `${selectedYear}-12-31`;
                periodKey = `yearly_${selectedYear}`;
                break;
        }

        if (!startDate || !endDate) {
            alert('يرجى تحديد الفترة الزمنية');
            return;
        }

        // حساب عدد الأيام في الفترة
        const start = new Date(startDate);
        const end = new Date(endDate);
        const totalDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

        let totalPayroll = 0;
        let totalWorkingDays = 0;
        let totalAbsentDays = 0;
        let totalHours = 0;
        let totalBonuses = 0;
        let totalDeductions = 0;

        this.workers.forEach(worker => {
            if (worker.status !== 'active') return;

            let presentDays = 0;
            let absentDays = 0;
            let totalWorkerHours = 0;

            // حساب أيام الحضور والغياب والساعات
            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                const dateStr = d.toISOString().split('T')[0];
                const attendance = this.attendance[dateStr] && this.attendance[dateStr][worker.id];

                if (attendance && (attendance.status === 'present' || attendance.status === 'late')) {
                    presentDays++;

                    // حساب ساعات العمل
                    if (attendance.arrivalTime && attendance.leaveTime) {
                        const arrival = new Date(`${dateStr}T${attendance.arrivalTime}`);
                        const leave = new Date(`${dateStr}T${attendance.leaveTime}`);
                        const diffMs = leave - arrival;
                        const diffHours = diffMs / (1000 * 60 * 60);
                        totalWorkerHours += diffHours;
                    }
                } else {
                    absentDays++;
                }
            }

            const basicSalary = presentDays * worker.dailySalary;
            const bonuses = 0; // سيتم تحديثها لاحقاً
            const deductions = 0; // سيتم تحديثها لاحقاً
            const netSalary = basicSalary + bonuses - deductions;

            totalPayroll += netSalary;
            totalWorkingDays += presentDays;
            totalAbsentDays += absentDays;
            totalHours += totalWorkerHours;
            totalBonuses += bonuses;
            totalDeductions += deductions;

            // حفظ بيانات المرتب
            if (!this.payroll[periodKey]) {
                this.payroll[periodKey] = {};
            }

            this.payroll[periodKey][worker.id] = {
                workerId: worker.id,
                workerName: worker.name,
                dailySalary: worker.dailySalary,
                presentDays,
                absentDays,
                totalHours: Math.round(totalWorkerHours * 100) / 100,
                bonuses,
                deductions,
                netSalary: basicSalary,
                finalSalary: netSalary,
                periodType,
                startDate,
                endDate,
                calculatedAt: new Date().toISOString()
            };
        });

        // تحديث الإحصائيات
        if (document.getElementById('totalPayroll')) {
            document.getElementById('totalPayroll').textContent = totalPayroll.toLocaleString() + ' ج.م';
            document.getElementById('workingDays').textContent = totalWorkingDays;
            document.getElementById('absentDays').textContent = totalAbsentDays;
            document.getElementById('totalHours').textContent = Math.round(totalHours);
            document.getElementById('totalBonuses').textContent = totalBonuses.toLocaleString() + ' ج.م';
            document.getElementById('totalDeductions').textContent = totalDeductions.toLocaleString() + ' ج.م';
        }

        this.savePayroll();
        this.loadPayrollTable(periodKey);
        alert(`تم حساب المرتبات بنجاح`);
    }

    // تحميل جدول المرتبات
    loadPayrollTable(month) {
        const tbody = document.getElementById('payrollTable');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (!month || !this.payroll[month]) return;

        Object.values(this.payroll[month]).forEach(payroll => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${payroll.workerName}</td>
                <td>${payroll.dailySalary} ج.م</td>
                <td>${payroll.presentDays}</td>
                <td>${payroll.absentDays}</td>
                <td>
                    <input type="number" class="form-control form-control-sm" value="${payroll.bonuses}"
                           onchange="app.updatePayrollBonuses('${month}', '${payroll.workerId}', this.value)">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm" value="${payroll.deductions}"
                           onchange="app.updatePayrollDeductions('${month}', '${payroll.workerId}', this.value)">
                </td>
                <td><strong>${(payroll.netSalary + payroll.bonuses - payroll.deductions).toLocaleString()} ج.م</strong></td>
                <td>
                    <button class="btn btn-sm btn-outline-success" onclick="app.printPayslip('${month}', '${payroll.workerId}')">
                        <i class="bi bi-printer"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // تحديث الحوافز
    updatePayrollBonuses(month, workerId, bonuses) {
        if (this.payroll[month] && this.payroll[month][workerId]) {
            this.payroll[month][workerId].bonuses = parseFloat(bonuses) || 0;
            this.savePayroll();
            this.loadPayrollTable(month);
        }
    }

    // تحديث الخصومات
    updatePayrollDeductions(month, workerId, deductions) {
        if (this.payroll[month] && this.payroll[month][workerId]) {
            this.payroll[month][workerId].deductions = parseFloat(deductions) || 0;
            this.savePayroll();
            this.loadPayrollTable(month);
        }
    }

    // طباعة كشف مرتب
    printPayslip(month, workerId) {
        const payroll = this.payroll[month] && this.payroll[month][workerId];
        if (!payroll) {
            alert('لا توجد بيانات مرتب لهذا العامل في الفترة المحددة');
            return;
        }

        const netSalary = payroll.netSalary + payroll.bonuses - payroll.deductions;
        const reportWindow = window.open('', '_blank');

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>كشف مرتب - ${payroll.workerName}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .company-info { text-align: center; margin-bottom: 20px; }
                .payslip-info { margin-bottom: 30px; }
                .info-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding: 5px 0; }
                .info-row:nth-child(even) { background-color: #f9f9f9; }
                .total-row { border-top: 2px solid #333; font-weight: bold; font-size: 1.2em; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="company-info">
                <h2>مصنع الشنط</h2>
                <p>كشف مرتب</p>
            </div>

            <div class="header">
                <h1>كشف مرتب - ${payroll.workerName}</h1>
                <p>الفترة: من ${new Date(payroll.startDate).toLocaleDateString('ar-EG')} إلى ${new Date(payroll.endDate).toLocaleDateString('ar-EG')}</p>
            </div>

            <div class="payslip-info">
                <div class="info-row">
                    <span>اسم العامل:</span>
                    <span><strong>${payroll.workerName}</strong></span>
                </div>
                <div class="info-row">
                    <span>الراتب اليومي:</span>
                    <span>${payroll.dailySalary} ج.م</span>
                </div>
                <div class="info-row">
                    <span>أيام الحضور:</span>
                    <span>${payroll.presentDays} يوم</span>
                </div>
                <div class="info-row">
                    <span>أيام الغياب:</span>
                    <span>${payroll.absentDays} يوم</span>
                </div>
                <div class="info-row">
                    <span>الراتب الأساسي:</span>
                    <span>${payroll.netSalary.toLocaleString()} ج.م</span>
                </div>
                <div class="info-row">
                    <span>الحوافز:</span>
                    <span>${payroll.bonuses} ج.م</span>
                </div>
                <div class="info-row">
                    <span>الخصومات:</span>
                    <span>${payroll.deductions} ج.م</span>
                </div>
                <div class="info-row total-row">
                    <span>صافي المرتب:</span>
                    <span><strong>${netSalary.toLocaleString()} ج.م</strong></span>
                </div>
            </div>

            <div style="margin-top: 50px; text-align: center;">
                <p>التاريخ: ${new Date().toLocaleDateString('ar-EG')}</p>
                <br><br>
                <div style="display: flex; justify-content: space-between;">
                    <div>
                        <p>توقيع العامل</p>
                        <p>_________________</p>
                    </div>
                    <div>
                        <p>توقيع المحاسب</p>
                        <p>_________________</p>
                    </div>
                </div>
            </div>

            <div class="footer">
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }

    // طباعة حضور اليوم
    printDailyAttendance() {
        const date = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        const reportWindow = window.open('', '_blank');

        let presentCount = 0, absentCount = 0, lateCount = 0;

        const attendanceRows = this.workers.filter(w => w.status === 'active').map(worker => {
            const attendance = this.attendance[date] && this.attendance[date][worker.id];
            const status = attendance ? attendance.status : 'absent';
            const arrivalTime = attendance ? attendance.arrivalTime : '';
            const leaveTime = attendance ? attendance.leaveTime : '';
            const notes = attendance ? attendance.notes : '';

            // حساب ساعات العمل
            let workingHours = '';
            if (arrivalTime && leaveTime) {
                const arrival = new Date(`${date}T${arrivalTime}`);
                const leave = new Date(`${date}T${leaveTime}`);
                const diffMs = leave - arrival;
                const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                workingHours = `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
            }

            if (status === 'present') presentCount++;
            else if (status === 'absent') absentCount++;
            else if (status === 'late') lateCount++;

            const statusText = status === 'present' ? 'حاضر' : status === 'absent' ? 'غائب' : 'متأخر';
            const statusClass = status === 'present' ? 'present' : status === 'absent' ? 'absent' : 'late';

            return `
                <tr class="${statusClass}">
                    <td>${worker.name}</td>
                    <td>${worker.job}</td>
                    <td>${statusText}</td>
                    <td>${arrivalTime}</td>
                    <td>${leaveTime}</td>
                    <td>${workingHours}</td>
                    <td>${notes}</td>
                </tr>
            `;
        }).join('');

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حضور يوم ${new Date(date).toLocaleDateString('ar-EG')}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
                .stats { display: flex; justify-content: space-around; margin-bottom: 30px; }
                .stat-box { border: 1px solid #ddd; padding: 15px; border-radius: 5px; text-align: center; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f5f5f5; }
                .present { background-color: #e8f5e8; }
                .absent { background-color: #ffebee; }
                .late { background-color: #fff3e0; }
                .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير حضور يوم ${new Date(date).toLocaleDateString('ar-EG')}</h1>
                <p>مصنع الشنط - نظام إدارة الحضور والانصراف</p>
            </div>

            <div class="stats">
                <div class="stat-box">
                    <h3 style="color: green;">${presentCount}</h3>
                    <p>حاضر</p>
                </div>
                <div class="stat-box">
                    <h3 style="color: red;">${absentCount}</h3>
                    <p>غائب</p>
                </div>
                <div class="stat-box">
                    <h3 style="color: orange;">${lateCount}</h3>
                    <p>متأخر</p>
                </div>
                <div class="stat-box">
                    <h3>${this.workers.filter(w => w.status === 'active').length}</h3>
                    <p>إجمالي العمال</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>اسم العامل</th>
                        <th>الوظيفة</th>
                        <th>الحالة</th>
                        <th>وقت الحضور</th>
                        <th>وقت الانصراف</th>
                        <th>ساعات العمل</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
                    ${attendanceRows}
                </tbody>
            </table>

            <div class="footer">
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }

    // طباعة حضور الأسبوع
    printWeeklyAttendance() {
        const selectedDate = document.getElementById('attendanceDate').value || new Date().toISOString().split('T')[0];
        const startDate = new Date(selectedDate);
        const dayOfWeek = startDate.getDay();
        const startOfWeek = new Date(startDate);
        startOfWeek.setDate(startDate.getDate() - dayOfWeek);

        const reportWindow = window.open('', '_blank');

        // إنشاء جدول أسبوعي
        const weekDays = [];
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(startOfWeek.getDate() + i);
            weekDays.push(date.toISOString().split('T')[0]);
        }

        const weeklyData = this.workers.filter(w => w.status === 'active').map(worker => {
            const weekData = {
                name: worker.name,
                job: worker.job,
                days: {}
            };

            weekDays.forEach(date => {
                const attendance = this.attendance[date] && this.attendance[date][worker.id];
                const status = attendance ? attendance.status : 'absent';
                const arrivalTime = attendance ? attendance.arrivalTime : '';
                const leaveTime = attendance ? attendance.leaveTime : '';

                weekData.days[date] = {
                    status: status,
                    arrivalTime: arrivalTime,
                    leaveTime: leaveTime
                };
            });

            return weekData;
        });

        const reportContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حضور أسبوعي</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
                .header { text-align: center; margin-bottom: 30px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 4px; text-align: center; font-size: 10px; }
                th { background-color: #f5f5f5; }
                .present { background-color: #e8f5e8; }
                .absent { background-color: #ffebee; }
                .late { background-color: #fff3e0; }
                .worker-name { text-align: right; font-weight: bold; }
                .footer { text-align: center; margin-top: 30px; font-size: 10px; color: #666; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير الحضور الأسبوعي</h1>
                <p>من ${new Date(weekDays[0]).toLocaleDateString('ar-EG')} إلى ${new Date(weekDays[6]).toLocaleDateString('ar-EG')}</p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th rowspan="2">اسم العامل</th>
                        <th rowspan="2">الوظيفة</th>
                        ${weekDays.map(date => `<th colspan="2">${new Date(date).toLocaleDateString('ar-EG', {weekday: 'short', day: 'numeric'})}</th>`).join('')}
                    </tr>
                    <tr>
                        ${weekDays.map(() => '<th>حضور</th><th>انصراف</th>').join('')}
                    </tr>
                </thead>
                <tbody>
                    ${weeklyData.map(worker => `
                        <tr>
                            <td class="worker-name">${worker.name}</td>
                            <td>${worker.job}</td>
                            ${weekDays.map(date => {
                                const dayData = worker.days[date];
                                const statusClass = dayData.status === 'present' ? 'present' : dayData.status === 'absent' ? 'absent' : 'late';
                                return `
                                    <td class="${statusClass}">${dayData.arrivalTime || '-'}</td>
                                    <td class="${statusClass}">${dayData.leaveTime || '-'}</td>
                                `;
                            }).join('')}
                        </tr>
                    `).join('')}
                </tbody>
            </table>

            <div class="footer">
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                <p>تصميم وإعداد: البشمهندس أحمد يونس | الهاتف: 01100693019</p>
            </div>
        </body>
        </html>
        `;

        reportWindow.document.write(reportContent);
        reportWindow.document.close();
        reportWindow.print();
    }

    // عرض رسالة خطأ محسنة
    showError(message) {
        // إنشاء عنصر التنبيه
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = `
            top: 100px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <strong>خطأ:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // عرض رسالة نجاح محسنة
    showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = `
            top: 100px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        alertDiv.innerHTML = `
            <i class="bi bi-check-circle-fill me-2"></i>
            <strong>نجح:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    // عرض رسالة تحذير محسنة
    showWarning(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = `
            top: 100px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        `;
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <strong>تحذير:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 4000);
    }

    // عرض رسالة معلومات محسنة
    showInfo(title, content = null) {
        if (content) {
            // إذا كان هناك محتوى HTML، عرضه في نافذة منبثقة
            const modalDiv = document.createElement('div');
            modalDiv.className = 'modal fade';
            modalDiv.innerHTML = `
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content neumorphic-card">
                        <div class="modal-header">
                            <h5 class="modal-title"><i class="bi bi-info-circle me-2"></i>${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            ${content}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modalDiv);
            const modal = new bootstrap.Modal(modalDiv);
            modal.show();

            // إزالة النافذة من DOM عند إغلاقها
            modalDiv.addEventListener('hidden.bs.modal', () => {
                modalDiv.remove();
            });
        } else {
            // عرض رسالة بسيطة
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-info alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = `
                top: 100px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            alertDiv.innerHTML = `
                <i class="bi bi-info-circle-fill me-2"></i>
                <strong>معلومة:</strong> ${title}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 4000);
        }
    }

    // تأكيد محسن
    showConfirm(message, onConfirm, onCancel = null) {
        const modalDiv = document.createElement('div');
        modalDiv.className = 'modal fade';
        modalDiv.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content neumorphic-card">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-question-circle me-2"></i>تأكيد
                        </h5>
                    </div>
                    <div class="modal-body">
                        <p class="mb-0">${message}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger confirm-btn">تأكيد</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modalDiv);
        const modal = new bootstrap.Modal(modalDiv);

        modalDiv.querySelector('.confirm-btn').addEventListener('click', () => {
            modal.hide();
            if (onConfirm) onConfirm();
        });

        modalDiv.addEventListener('hidden.bs.modal', () => {
            modalDiv.remove();
            if (onCancel) onCancel();
        });

        modal.show();
    }

    // إضافة الشهر القادم
    addNextMonth() {
        const monthSelect = document.getElementById('payrollMonth');
        const currentDate = new Date();
        const nextMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
        const nextMonthValue = nextMonth.toISOString().slice(0, 7);
        const nextMonthText = nextMonth.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long' });

        // التحقق من عدم وجود الشهر مسبقاً
        const existingOption = Array.from(monthSelect.options).find(option => option.value === nextMonthValue);
        if (existingOption) {
            this.showWarning('الشهر القادم موجود بالفعل في القائمة');
            monthSelect.value = nextMonthValue;
            return;
        }

        // إضافة الشهر الجديد
        const option = document.createElement('option');
        option.value = nextMonthValue;
        option.textContent = nextMonthText;
        monthSelect.appendChild(option);
        monthSelect.value = nextMonthValue;

        this.showSuccess(`تم إضافة ${nextMonthText} إلى قائمة الشهور`);
    }

    // إضافة السنة القادمة
    addNextYear() {
        const yearSelect = document.getElementById('payrollYear');
        const currentYear = new Date().getFullYear();
        const nextYear = currentYear + 1;

        // التحقق من عدم وجود السنة مسبقاً
        const existingOption = Array.from(yearSelect.options).find(option => option.value === nextYear.toString());
        if (existingOption) {
            this.showWarning('السنة القادمة موجودة بالفعل في القائمة');
            yearSelect.value = nextYear.toString();
            return;
        }

        // إضافة السنة الجديدة
        const option = document.createElement('option');
        option.value = nextYear.toString();
        option.textContent = nextYear.toString();
        yearSelect.appendChild(option);
        yearSelect.value = nextYear.toString();

        this.showSuccess(`تم إضافة سنة ${nextYear} إلى قائمة السنوات`);
    }
}

// تهيئة التطبيق
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new BagFactorySystem();
});

// دوال عامة
function logout() {
    app.logout();
}

function showSection(sectionName) {
    app.showSection(sectionName);
}

// دوال النوافذ المنبثقة
function showAddProductModal() {
    app.resetProductModal();
    app.populateProductMaterialAndCategoryDropdowns(); // Populate dropdowns before showing modal
    const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
    modal.show();
}

function showAddMaterialModal() {
    const modal = new bootstrap.Modal(document.getElementById('addMaterialModal'));
    modal.show();
}

function showEditMaterialModal() {
    const modal = new bootstrap.Modal(document.getElementById('editMaterialModal'));
    modal.show();
}

function showAddCategoryModal() {
    const modal = new bootstrap.Modal(document.getElementById('addCategoryModal'));
    modal.show();
}

function showEditCategoryModal() {
    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
    modal.show();
}

function showAddWorkerModal() {
    const modal = new bootstrap.Modal(document.getElementById('addWorkerModal'));
    modal.show();
}

function showAddUserModal() {
    const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
    modal.show();
}

function showAddStockModal() {
    // ملء قائمة المنتجات
    const select = document.getElementById('stockProductSelect');
    select.innerHTML = '<option value="">اختر المنتج</option>';

    app.products.forEach(product => {
        const option = document.createElement('option');
        option.value = product.id;
        option.textContent = `${product.code} - ${product.name} (الكمية الحالية: ${product.quantity})`;
        select.appendChild(option);
    });

    // تعيين التاريخ الحالي كقيمة افتراضية
    const dateInput = document.getElementById('stockDate');
    if (dateInput) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }

    const modal = new bootstrap.Modal(document.getElementById('addStockModal'));
    modal.show();
}

function showWorkerWithdrawalModal() {
    app.showWorkerWithdrawalModal();
}

// دوال المنتجات
function addProduct() {
    app.addProduct();
}

function addMaterial() {
    app.addMaterial();
}

function updateMaterial() {
    app.updateMaterial();
}

function deleteMaterial(id) {
    app.deleteMaterial(id);
}

function addCategory() {
    app.addCategory();
}

function updateCategory() {
    app.updateCategory();
}

function deleteCategory(id) {
    app.deleteCategory(id);
}

function generateBarcode() {
    app.generateBarcode();
}

// دوال العمال
function addWorker() {
    app.addWorker();
}

// دوال المستخدمين
function addUser() {
    app.addUser();
}

// دوال الحضور
function markAllPresent() {
    app.markAllPresent();
}

// دوال المرتبات
function calculatePayroll() {
    app.calculatePayroll();
}

// دوال التقارير
function exportToExcel() {
    app.exportToExcel();
}

function exportToPDF() {
    app.exportToPDF();
}

function generateProductReport() {
    app.generateProductReport();
}

function generateLowStockReport() {
    app.generateLowStockReport();
}

function generateProductionReport() {
    app.generateProductionReport();
}

function generateWorkersReport() {
    app.generateWorkersReport();
}

function generateAttendanceReport() {
    app.generateAttendanceReport();
}

function generatePayrollReport() {
    app.generatePayrollReport();
}

function generateWorkerWithdrawalsReport() {
    app.printWorkerWithdrawalsReport();
}

// دوال الإعدادات
function exportData() {
    app.exportData();
}

function importData() {
    app.importData();
}

// دوال المخزون
function processStockOperation() {
    app.processStockOperation();
}

function addStock(productId) {
    app.addStock(productId);
}

function removeStock(productId) {
    app.removeStock(productId);
}

function printPayslip(month, workerId) {
    app.printPayslip(month, workerId);
}

// دوال إضافية للمستخدمين والحضور
function deleteUser(username) {
    app.deleteUser(username);
}

function updateAttendanceNotes(workerId, date, notes) {
    app.updateAttendanceNotes(workerId, date, notes);
}

function markPresent(workerId, date) {
    app.markPresent(workerId, date);
}

function updatePayrollBonuses(month, workerId, bonuses) {
    app.updatePayrollBonuses(month, workerId, bonuses);
}

function updatePayrollDeductions(month, workerId, deductions) {
    app.updatePayrollDeductions(month, workerId, deductions);
}

function updateAttendance(workerId, date, status) {
    app.updateAttendance(workerId, date, status);
}

function editProduct(productId) {
    app.editProduct(productId);
}

function deleteProduct(productId) {
    app.deleteProduct(productId);
}

function editWorker(workerId) {
    app.editWorker(workerId);
}

function deleteWorker(workerId) {
    app.deleteWorker(workerId);
}

// دوال الحضور المحسنة
function markAllLeave() {
    app.markAllLeave();
}

function printDailyAttendance() {
    app.printDailyAttendance();
}

function printWeeklyAttendance() {
    app.printWeeklyAttendance();
}

// دوال المرتبات المحسنة
function addNextMonth() {
    app.addNextMonth();
}

function addNextYear() {
    app.addNextYear();
}

function updateArrivalTime(workerId, date, time) {
    app.updateArrivalTime(workerId, date, time);
}

function updateLeaveTime(workerId, date, time) {
    app.updateLeaveTime(workerId, date, time);
}

function markLeave(workerId, date) {
    app.markLeave(workerId, date);
}

// دوال فلترة سجلات سحب العمال
function clearWithdrawalFilters() {
    app.clearWithdrawalFilters();
}

// دوال إعادة ضبط المصنع
function showFactoryResetModal() {
    const modal = new bootstrap.Modal(document.getElementById('factoryResetModal'));
    modal.show();
}

function confirmFactoryReset() {
    const confirmation = document.getElementById('resetConfirmation').value;
    const adminPassword = document.getElementById('adminPasswordConfirm').value;

    // التحقق من النص التأكيدي
    if (confirmation !== 'إعادة ضبط المصنع') {
        alert('يرجى كتابة "إعادة ضبط المصنع" بالضبط للتأكيد');
        return;
    }

    // التحقق من كلمة مرور المدير
    if (!app.users['admin'] || app.users['admin'].password !== adminPassword) {
        alert('كلمة مرور المدير غير صحيحة');
        return;
    }

    // تأكيد أخير
    if (confirm('هل أنت متأكد تماماً من إعادة ضبط المصنع؟ سيتم مسح جميع البيانات نهائياً!')) {
        app.factoryReset();
    }
}

// دوال حركة المخزون الجديدة
function clearInventoryMovementFilters() {
    app.clearInventoryMovementFilters();
}

function printInventoryMovementReport() {
    app.printInventoryMovementReport();
}

function viewInventoryMovementDetails(movementId) {
    app.viewInventoryMovementDetails(movementId);
}
